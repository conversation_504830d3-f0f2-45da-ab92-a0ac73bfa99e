"use client";
import Link from "next/link";
import ShinyText from "@/components/ShinyText";

export default function BusinessOwnersLandingPage() {
    return (
        <main id="main-content" className="flex flex-col items-center min-h-screen space-y-8">
            {/* Hero Section */}
            <section className="w-full py-5 md:pt-16 md:pb-4">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-2">
                        <ShinyText
                            text="Empower Your Mobile Business"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-lg text-white/90 max-w-3xl mx-auto mb-4">
                        It's time to take your business to the next level.
                    </p>
                </div>
            </section>

            {/* Options Section */}
            <section className="w-full py-7 bg-white/50 backdrop-blur-xl border border-white/20 rounded-xl mx-4 mb-8">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {/* Newsletter Signup Card */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                INSIGHTS & TIPS
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">📬</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Inside Secrets Newsletter</h3>
                            <p className="text-gray-700 mb-4">
                                Get exclusive insights and tips delivered to your inbox, helping you stay ahead in the detailing industry.
                            </p>
                            <Link
                                href="/newsletter"
                                className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-[5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-sm">Sign Up Now</span>
                                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </Link>
                        </div>

                        {/* Franchise Opportunity Card */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                WE'LL GIVE YOU THE TOOLS AND CLIENTS
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🚀</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Open Your Mobile Business</h3>
                            <p className="text-gray-700 mb-4">
                                Start your business with a system built for success starting at $300.
                            </p>
                            <Link
                                href="/franchise"
                                className="group relative bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:-translate-y-[5px] hover:shadow-lg hover:shadow-blue-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-sm">Learn More</span>
                                <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </Link>
                        </div>

                        {/* Free Software Card */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                5X REVENUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🛠️</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Free Booking Software</h3>
                            <p className="text-gray-700 mb-4">
                                Streamline your operations with our free software, designed to save you time and effort.
                            </p>
                            <Link
                                href="/easybook"
                                className="group relative bg-green-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-600 transition-all transform hover:scale-105 hover:-translate-y-[5px] hover:shadow-lg hover:shadow-green-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-sm">Get Started</span>
                                <span className="absolute inset-0 bg-gradient-to-r from-green-500 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </Link>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
}
// lib/taxCalculator.ts

// Define the types for your tax calculation
export interface TaxInput {
    addressStreet: string;
    addressCity: string;
    addressState: string;
    addressPostal: string;
    addressCountry: string;
    amount: number;
  }
  
  export interface TaxCalculationResult {
    totalBeforeTax: string;
    totalTax: string;
    totalAfterTax: string;
    taxRate: string;
    error?: string;
  }
  
  export async function calculateTax(taxData: TaxInput): Promise<TaxCalculationResult> {
    console.log('🧮 TaxCalculator: Starting tax calculation', {
      taxData,
      timestamp: new Date().toISOString()
    });

    try {
      // Format the address for Stripe API
      const address = {
        line1: taxData.addressStreet,
        city: taxData.addressCity,
        state: taxData.addressState,
        postal_code: taxData.addressPostal,
        country: taxData.addressCountry,
      };

      console.log('🧮 TaxCalculator: Formatted address for API', address);

      const requestBody = {
        amount: Math.round(taxData.amount * 100), // Convert to cents
        address: address,
      };

      console.log('🧮 TaxCalculator: Making API request to /api/tax-calculation', requestBody);

      // Call your server-side API endpoint
      const response = await fetch('/api/tax-calculation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('🧮 TaxCalculator: API response status', {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText
      });

      if (!response.ok) {
        throw new Error(`Tax calculation failed: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🧮 TaxCalculator: API response data', data);

      if (!data.success) {
        console.error('🧮 TaxCalculator: API returned error', data.error);
        throw new Error(data.error || 'Unknown tax calculation error');
      }

      // Format the response to match what BookingSection expects
      const taxAmount = data.taxAmount / 100; // Convert cents to dollars
      // data.taxPercentage is already the correct percentage as a string
      const taxRate = data.taxPercentage.toString();

      const result = {
        totalBeforeTax: taxData.amount.toFixed(2),
        totalTax: taxAmount.toFixed(2),
        totalAfterTax: (taxData.amount + taxAmount).toFixed(2),
        taxRate: taxRate
      };

      console.log('🧮 TaxCalculator: Returning formatted result', result);
      return result;
    } catch (error) {
      console.error('Tax calculation error:', error);
      
      // Return a fallback with no tax if calculation fails
      return {
        totalBeforeTax: taxData.amount.toFixed(2),
        totalTax: '0.00',
        totalAfterTax: taxData.amount.toFixed(2),
        taxRate: '0.00',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

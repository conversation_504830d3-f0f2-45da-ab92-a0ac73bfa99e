'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import Navbar from './navbar';
import Footer from './footer';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  const [shouldHideNavFooter, setShouldHideNavFooter] = useState(false);

  useEffect(() => {
    // Check if we're on a payment page
    const isPaymentPage = pathname?.startsWith('/pay/');

    if (isPaymentPage) {
      // Check if the page is showing "Loading your checkout..." or payment form
      // We'll hide nav/footer until payment is complete
      const checkPaymentStatus = () => {
        // Look for specific text content to determine payment status
        const bodyText = document.body.textContent || '';

        // If we see "Payment confirmed" or receipt content, payment is complete
        const isPaymentComplete = bodyText.includes('Payment confirmed') ||
                                 bodyText.includes('RECEIPT') ||
                                 bodyText.includes('✓ Payment confirmed');

        // If we see loading text or payment form, hide nav/footer
        const isLoading = bodyText.includes('Loading your checkout...') ||
                         bodyText.includes('Complete your payment');

        // Hide nav/footer during loading or payment process, show after completion
        setShouldHideNavFooter(isLoading || !isPaymentComplete);
      };

      // Initial check
      checkPaymentStatus();

      // Set up observer to watch for DOM changes
      const observer = new MutationObserver(checkPaymentStatus);
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return () => observer.disconnect();
    } else {
      // Not a payment page, always show nav/footer
      setShouldHideNavFooter(false);
    }
  }, [pathname]);

  return (
    <>
      {!shouldHideNavFooter && <Navbar />}
      <main className={shouldHideNavFooter ? "flex-grow" : "pt-20 flex-grow container mx-auto px-1 sm:px-1"}>
        {children}
      </main>
      {!shouldHideNavFooter && <Footer />}
    </>
  );
}

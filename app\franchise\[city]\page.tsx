import Link from "next/link";

const cityData: Record<string, any> = {
  "kansas-city": {
    city: "Kansas City, MO/KS",
    population: "2.2 million",
    vehicles: "1.5 million registered vehicles",
    income: "Median household income: $68,000",
    nearby: "Overland Park, Independence, Olathe",
    heroImg: "/exterior wash 1.jpg",
    alt: "Mobile Detailing Franchise Kansas City",
    marketStats: "Kansas City’s booming metro, high car ownership, and strong economy make it a plush spot for a mobile detailing franchise.",
    competition: "Plenty of cars, not enough premium mobile detailers. Room to grow and dominate.",
    growth: "Rapidly expanding suburbs and a love for clean rides.",
  },
  "dallas-fort-worth": {
    city: "Dallas-Fort Worth, TX",
    population: "7.6 million",
    vehicles: "5.2 million registered vehicles",
    income: "Median household income: $75,000",
    nearby: "Plano, Arlington, Irving",
    heroImg: "/exterior wash 2.jpg",
    alt: "Mobile Detailing Franchise Dallas-Fort Worth",
    marketStats: "DFW is all about hustle, highways, and hot cars. People want convenience and quality, fast.",
    competition: "Big market, but most detailers are old-school. Mobile is the future.",
    growth: "DFW keeps sprawling—your territory grows with it.",
  },
  "houston": {
    city: "Houston, TX",
    population: "7.3 million",
    vehicles: "5.1 million registered vehicles",
    income: "Median household income: $70,000",
    nearby: "Sugar Land, The Woodlands, Pasadena",
    heroImg: "/exterior wash 3.jpg",
    alt: "Mobile Detailing Franchise Houston",
    marketStats: "Houston’s heat and humidity mean cars get dirty fast. People want their rides plush and protected.",
    competition: "Lots of cars, not enough mobile pros. Big opportunity.",
    growth: "Houston’s growth is off the charts—so is the demand for convenience.",
  },
  "denver": {
    city: "Denver, CO",
    population: "3 million",
    vehicles: "2 million registered vehicles",
    income: "Median household income: $80,000",
    nearby: "Aurora, Lakewood, Boulder",
    heroImg: "/exterior wash 4.jpg",
    alt: "Mobile Detailing Franchise Denver",
    marketStats: "Denver drivers love their cars clean—especially after mountain adventures.",
    competition: "Few mobile options, tons of potential.",
    growth: "Denver’s booming, and so is the need for on-the-go detailing.",
  },
  "phoenix": {
    city: "Phoenix, AZ",
    population: "5 million",
    vehicles: "3.5 million registered vehicles",
    income: "Median household income: $72,000",
    nearby: "Scottsdale, Mesa, Glendale",
    heroImg: "/ceramic1.jpg",
    alt: "Mobile Detailing Franchise Phoenix",
    marketStats: "Phoenix sun = dusty cars. People want quick, quality cleanups.",
    competition: "Traditional shops can’t keep up with mobile demand.",
    growth: "Phoenix is one of the fastest-growing cities in the US.",
  },
};

export async function generateMetadata({ params }: { params: { city: string } }) {
  const data = cityData[params.city];
  return {
    title: `${data.city} Franchise | Detail On The Go`,
    description: `Start your mobile car detailing franchise in ${data.city}. Proven business model, we come to customers. Low investment, high returns. Protected territory available.`,
    alternates: {
      canonical: `https://detailongo.com/franchise/${params.city}`,
    },
    openGraph: {
      title: `Mobile Detailing Franchise ${data.city} | Detail On The Go`,
      description: `Start your mobile car detailing franchise in ${data.city}. Proven business model, we come to customers. Low investment, high returns. Protected territory available.`,
      url: `https://detailongo.com/franchise/${params.city}`,
      images: [
        {
          url: data.heroImg,
          width: 1200,
          height: 630,
          alt: data.alt,
        },
      ],
      type: "website",
    },
  };
}

export default function FranchiseCityPage({ params }: { params: { city: string } }) {
  const data = cityData[params.city];
  if (!data) return <div>Location not found.</div>;

  return (
    <main>
      {/* Hero Section */}
      <section className="py-8 px-4 text-center bg-palatinate_blue-900 text-white">
        <h1 className="text-3xl font-bold mb-2">
          Mobile Detailing Franchise Opportunity in {data.city}
        </h1>
        <p className="text-lg mb-4">
          Ready to own your future? Start a mobile detailing business in {data.city} with Detail On The Go. We come to you, you own the territory.
        </p>
        <a
          href="#get-started"
          className="inline-block bg-palatinate_blue-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg"
        >
          Get Franchise Info
        </a>
        <img
          src={data.heroImg}
          alt={data.alt}
          className="w-full max-w-md mx-auto mt-6 rounded-lg shadow-md"
        />
      </section>

      {/* Why [City] Section */}
      <section className="py-8 px-4 bg-white text-gray-900">
        <h2 className="text-2xl font-semibold mb-4">Why {data.city}?</h2>
        <ul className="list-disc list-inside text-lg space-y-2">
          <li>{data.marketStats}</li>
          <li>Population: {data.population}</li>
          <li>Vehicles: {data.vehicles}</li>
          <li>{data.income}</li>
          <li>Nearby cities: {data.nearby}</li>
        </ul>
      </section>

      {/* Our Mobile Franchise Model */}
      <section className="py-8 px-4 bg-gray-50 text-gray-900">
        <h2 className="text-2xl font-semibold mb-4">Our Mobile Franchise Model</h2>
        <p className="mb-4">
          We’re the go-to mobile detailing experts in {data.city}. Our plush, fluffy-fresh, clean service comes to your customers—wherever they are. No shop rent, no waiting rooms, just premium quality and time efficiency. Services include:
        </p>
        <ul className="list-disc list-inside space-y-2">
          <li>Exterior hand wash & wax</li>
          <li>Interior deep clean & plush vacuum</li>
          <li>Boat & RV detailing</li>
          <li>Ceramic coating</li>
          <li>Pet hair annihilation</li>
          <li>Fleet & commercial services</li>
        </ul>
        <p className="mt-4">
          <strong>We come to you.</strong> Your customers love it, and so will you.
        </p>
      </section>

      {/* Investment Details */}
      <section className="py-8 px-4 bg-white text-gray-900">
        <h2 className="text-2xl font-semibold mb-4">Investment Details</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>Total investment: $35,000–$65,000 (low overhead, high return)</li>
          <li>Protected territory: up to 250,000 population</li>
          <li>Franchise package: training, equipment, marketing, support</li>
          <li>ROI: Most owners see payback in 12–18 months</li>
        </ul>
      </section>

      {/* Success in [City] */}
      <section className="py-8 px-4 bg-gray-50 text-gray-900">
        <h2 className="text-2xl font-semibold mb-4">Success in {data.city}</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>Market size: {data.population} and growing</li>
          <li>Competition: {data.competition}</li>
          <li>Growth potential: {data.growth}</li>
        </ul>
        <p className="mt-4">
          <strong>Ready to be the go-to mobile detailing expert in {data.city}?</strong>
        </p>
      </section>

      {/* Get Started Section */}
      <section id="get-started" className="py-8 px-4 bg-palatinate_blue-900 text-white">
        <h2 className="text-2xl font-semibold mb-4">Get Started</h2>
        <p className="mb-4">
          Join our proven franchise model. Fill out the form and we’ll get you rolling!
        </p>
        <form className="max-w-lg mx-auto grid gap-4">
          <input
            type="text"
            name="name"
            placeholder="Your Name"
            required
            className="p-3 rounded"
            aria-label="Your Name"
          />
          <input
            type="email"
            name="email"
            placeholder="Your Email"
            required
            className="p-3 rounded"
            aria-label="Your Email"
          />
          <input
            type="tel"
            name="phone"
            placeholder="Your Phone"
            required
            className="p-3 rounded"
            aria-label="Your Phone"
          />
          <button
            type="submit"
            className="bg-palatinate_blue-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg"
          >
            Request Franchise Info
          </button>
        </form>
        <p className="mt-4 text-sm">
          Next steps: We’ll reach out, answer your questions, and show you how easy it is to start detailing business in {data.city}.
        </p>
        <Link href="/franchise" className="underline text-white mt-2 block">
          Back to Franchise Overview
        </Link>
      </section>

      {/* Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": ["LocalBusiness", "ProfessionalService"],
            "name": `Detail On The Go - ${data.city}`,
            "image": data.heroImg,
            "url": `https://detailongo.com/franchise/${params.city}`,
            "telephone": "(*************",
            "address": {
              "@type": "PostalAddress",
              "addressLocality": data.city,
              "addressCountry": "USA"
            },
            "areaServed": data.city,
            "description": `Mobile detailing franchise ${data.city}. We come to you. Premium quality, time efficient, low investment.`,
            "makesOffer": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Mobile Car Detailing",
                  "description": "Professional mobile car detailing services"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "RV Detailing",
                  "description": "Specialized RV cleaning and detailing services"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Boat Detailing",
                  "description": "Professional boat cleaning and detailing services"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Ceramic Coating",
                  "description": "Premium ceramic coating protection services"
                }
              }
            ]
          }),
        }}
      />
    </main>
  );
}

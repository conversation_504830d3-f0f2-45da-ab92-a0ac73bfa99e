import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Systems | Detail On The Go',
    description: 'Discover the systems and processes that power Detail On The Go’s mobile detailing operations and franchise success.',
    keywords: [
        'systems',
        'business systems',
        'detailing systems',
        'Detail On The Go systems',
        'processes',
        'operations'
    ],
    alternates: {
        canonical: 'https://detailongo.com/sysgtems/',
    },
    openGraph: {
        title: 'Systems | Detail On The Go',
        description: 'Learn about the systems and processes behind our mobile detailing and franchise operations.',
        url: 'https://detailongo.com/sysgtems/',
        images: [
            {
                url: '/images/systems-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Systems - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Systems | Detail On The Go',
        description: 'Learn about the systems and processes behind our mobile detailing and franchise operations.',
        images: ['/images/systems-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Ahrefs Analytics Script */}
            <script
                src="https://analytics.ahrefs.com/analytics.js"
                data-key="stjttnR8VH1CxaQKx6GwfQ"
                async
            />
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "WebPage",
                        "name": "Systems",
                        "url": "https://detailongo.com/sysgtems/",
                        "description": "Discover the systems and processes that power Detail On The Go’s mobile detailing operations and franchise success.",
                        "publisher": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/systems-og.jpg",
                            "telephone": "******-615-6156"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}

"use client";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from 'next/navigation';


import AddressInput2 from "@/components/AddressInput2";
import { calculateTax, TaxCalculationResult } from '../lib/taxCalculator';
import { getBranchConfig, getBranchPackages, getBranchVehicleSizes, getBranchAddons } from '../lib/firebase/branchConfigService';

import SchedulingPage from '@/components/SchedulingPage';
import ServicePopup from '@/components/ServicePopup';

type Branch = "lwr" | "w-stl" | "dvr" | "ny";
type Addon = {
  name: string;
  price: number;
  hasQuantity: boolean;
  description: string;
  images: string[];
  branches: string[]; // Required
};

type VehicleConfig = {
  car: {
    sizes: string[];
    sizeMultipliers: number[];
    packages: Array<{
      name: string;
      price: number;
      images: string[];
      branches: string[];

    }>;
    addons: Array<{
      name: string;
      price: number;
      hasQuantity: boolean;
      description: string;
      images: string[];
      branches: string[];

    }>;
  };
  boat: {
    services: Array<{  // Changed from packages to services
      name: string;
      price: number;
      description: string;
      images: string[];
      branches: string[];

    }>;
    addons: Array<{
      name: string;
      price: number;
      hasQuantity: boolean;
      description: string;
      images: string[];
      branches: string[];

    }>;
  };
  rv: {
    services: Array<{  // Changed from packages to services
      name: string;
      price: number;
      description: string;
      images: string[];
      branches: string[];

    }>;
    addons: Array<{
      name: string;
      price: number;
      hasQuantity: boolean;
      description: string;
      images: string[];
      branches: string[];

    }>;
  };
};

const vehicleConfig = {
  car: {
    sizes: ["Sedan", "Small SUV (2 Rows)", "Large-SUV (3 Rows)", "Small/Mid-Truck", "Large-Truck", "Van (4+ Rows)"],
    sizeMultipliers: [1, 1.1, 1.15, 1.05, 1.1, 1.7],

    packages: [
      {
        name: "Interior Detailing",
        price: 248,
        images: [
          "/interior detailing 1.png",
          "/interior detailing 2.png",
          "/interior detailing 3.jpg",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Exterior Detailing",
        price: 99,
        images: [
          "/exterior wash 1.jpg",
          "/exterior wash 2.jpg",
          "/exterior wash 3.jpg",
          "/exterior wash 4.jpg",
          "/exterior wash 5.png",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Interior & Exterior Detailing",
        price: 260,
        images: [
          "/exterior wash 5.png",
          "/interior detailing 3.jpg",
          "/exterior wash 3.jpg",
          "/interior detailing 2.png",
          "/exterior wash 4.jpg",
          "/interior detailing 1.png"
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      }
    ],
    addons: [
      {
        name: "Window Shield Ceramic Coating",
        price: 25,
        hasQuantity: false,
        description: "Get crystal-clear visibility in any weather. Our advanced coating repels water, dirt, and debris for safer, easier driving.",
        images: [
          "/windowceramic5.png",
          "/windowceramic4.png",
          "/windowceramic3.jpg",
          "/windowceramic2.jpg",
          "/windowceramic1.jpeg"
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Engine Clean",
        price: 50,
        hasQuantity: false,
        description: "Boost your engine's performance and longevity. Professional cleaning removes built-up grime and prevents potential issues.",
        images: [
          "/engine clean 1.JPG"
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Full Body Ceramic Coating",
        price: 600,
        hasQuantity: false,
        description: "Ultimate paint protection. Achieve a showroom shine while defending against UV rays, chemicals, and minor scratches.",
        images: [
          "/ceramic1.jpg",
          "/ceramic2.jpg",
          "/ceramic3.jpg",
          "/ceramic4.png",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Scratch Removal / Per Body Panel",
        price: 200,
        hasQuantity: true,
        description: "Restore your car's flawless finish. Professional scratch removal to keep your vehicle looking brand new.",
        images: [
          "/polishing 1.jpg",
          "/polishing 2.jpg",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Headlight Restoration",
        price: 70,
        hasQuantity: true,
        description: "Bring back crystal-clear headlights. Improve night visibility and your car's overall appearance.",
        images: [
          "/headlights1.jpg",
          "/headlights2.jpg",
        ],
        branches: ["lwr", "w-stl"]
      },
      {
        name: "Paint Correction",
        price: 600,
        hasQuantity: false,
        description: "Eliminate imperfections for a flawless, showroom-quality finish. Perfect preparation for ceramic coating.",
        images: [
          "/polishing3.png",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Oil Change, Washer Fluid & Tire Inflate",
        price: 100,
        hasQuantity: false,
        description: "Eliminate imperfections for a flawless, showroom-quality finish. Perfect preparation for ceramic coating.",
        images: [
          "/oil change 1.jpg",
          "/oil change 2.jpg",
          "/oil change 3.png",
        ],
        branches: []
      },
      {
        name: "Window Rock Chip Repair",
        price: 200,
        hasQuantity: false,
        description: "Stop small chips from becoming big problems. Quick, professional repair to prevent windshield damage.",
        images: [
          "/rock chip 1.png",
          "/rock chip 2.png",
          "/rock chip 3.png",
        ],
        branches: ["lwr"]
      },
      {
        name: "Window Replacement",
        price: 400,
        hasQuantity: false,
        description: "Comprehensive windshield replacement. Restore your vehicle's safety and clarity.",
        images: [
          "/window replacement 1.png",
        ],
        branches: ["lwr"]
      }
    ],
  },
  boat: {
    services: [
      {
        name: "Vacuum & Wipedown / Pressure Wash Interior",
        price: 10,
        description: "Deep clean every corner. Refresh your boat's interior to like-new condition.",
        images: [
          "/boatinterior1.jpg",
          "/boat detailing 1.jpg",
        ],
        branches: ["lwr", "w-stl"]
      },
      {
        name: "Boat Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry",
        price: 15,
        description: "Complete exterior care. Remove marine buildup and protect your boat's finish.",
        images: [
          "/boatbottom.jpg",
          "/boat washing.png",
        ],
        branches: ["lwr", "w-stl"]
      },
      {
        name: "1 Step Deep Polish / Oxidation Removal / Heavy Algae Removal",
        price: 18,
        description: "Restore your boat's shine. Aggressive cleaning and protection in one service.",
        images: [
          "/boatpolishing.jpg",
          "boat polishing 2.jpg"
        ],
        branches: ["lwr", "w-stl"]
      },
      {
        name: "Boat Ceramic Coating",
        price: 50,
        description: "Ultimate marine protection. Shield your boat from UV and saltwater damage.",
        images: ["/boat ceramic.png"],
        branches: ["lwr", "w-stl"]
      }
    ],
    addons: [] as Addon[],
  },
  rv: {
    services: [
      {
        name: "RV Exterior Wash, Spray Wax & Towel Dry",
        price: 15,
        description: "Complete exterior revival. Remove dirt and protect your RV's finish.",
        images: [
          "/rv1.jpg",
          "/rv2.png",
          "/RV wash.png",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "Roof Cleaning",
        price: 5,
        description: "Comprehensive roof care. Clean and seal to prevent leaks and damage.",
        images: [
          "/rvroof1.jpg",
          "/rvroof2.jpg",
          "/rvroof3.jpg",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "One Step Deep Polish / Oxidation Removal",
        price: 18,
        description: "Revive and protect. Eliminate oxidation and restore your RV's exterior.",
        images: [
          "/rv polishing.png",
        ],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      },
      {
        name: "RV Ceramic Coating",
        price: 40,
        description: "Long-lasting protection. Shield your RV from environmental damage.",
        images: ["/RV ceramic.png"],
        branches: ["lwr", "w-stl", "dvr", "ny"]
      }
    ],
    addons: [] as Addon[],
  },
};

type VehicleType = keyof VehicleConfig;
type PackageType<T extends VehicleType> = T extends "car" ? keyof VehicleConfig[T]["packages"] : never;
type AddonType<T extends VehicleType> = keyof VehicleConfig[T]["addons"];

type CarBooking = {
  type: "car";
  size: string;
  year: string;
  make: string;
  model: string;
  package: string;
  petHair: 'yes' | 'no' | null;
  petHairLevel: 1 | 2 | 3 | null;
  addons: { name: string; quantity: number }[];
  basePrice: number;
  addonsPrice: number;
  totalPrice: number;
};


type BoatBooking = {
  type: "boat";
  services: string[];
  length?: number;
  addons: { name: string; quantity: number }[];
  basePrice: number;
  addonsPrice: number;
  totalPrice: number;
};

type RVBooking = {
  type: "rv";
  services: string[];
  length?: number;
  addons: { name: string; quantity: number }[];
  basePrice: number;
  addonsPrice: number;
  totalPrice: number;
};

type VehicleBooking = CarBooking | BoatBooking | RVBooking;
interface BranchDetails {
  branch: string;
  businessNumber: string;
  calendarId: string;
  collectionId: string;
  location: string;
  link: string;
  introMp3: string;
  allowedEmails: string[];
  employeeName: string;
  employeeNumber: string;
  reviewLink: string;
  lat: number;
  lng: number;
  distance?: number;
}



interface Address {
  address: string;
  lat?: number;
  lng?: number;
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

interface FormData {
  vehicles: VehicleBooking[];
  date: string;
  time: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: {  // Update this to match the Address interface
    address: string;
    lat?: number;
    lng?: number;
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };

  petHair: 'yes' | 'no' | null;
  petHairLevel: 1 | 2 | 3 | null;
  calculatedPrice: number;
  nameOnCard: string;
  cardPhone: string;
  cardEmail: string;
  waterElectricity: string;
  garageParking: string;
  bathroomAvailable: string;
  howFound: string;
  plan: string;
  notes: string;
  termsAgreed: boolean;
  isRecurring: boolean;
  frequency: string;
  stripeCustomerId: string;
  stripeName: string;
  stripeEmail: string;
  stripePhone: string;
  stripeAddress: {
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  branch?: Branch;

}
interface PaymentSectionProps {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  taxInfo: TaxInfo;
  onSubmit: (customerId: string) => void;
  setIsSubmitting: (value: boolean) => void; // Add this line
}



interface TaxInfo {
  totalBeforeTax: number; // Changed from string
  taxRate: number;
  totalTax: number;
  totalAfterTax: number;
  error?: string;
}



interface VehicleFormProps {
  index: number;
  vehicle: VehicleBooking;
  updateVehicle: (index: number, updates: Partial<VehicleBooking>) => void;
  removeVehicle: () => void;
  branch: Branch; // Add branch prop
  vehicleConfig: VehicleConfig; // Add vehicleConfig prop
}

const BookingSection: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  
  // Add logging on component mount
  useEffect(() => {
    console.log('🚗 BookingSection component loaded - Initializing pricing system...');
  }, []);

  const createPaymentLink = async (bookingData: any, bookingId: string) => {
    try {
      // Calculate total amount from vehicles
      const totalAmount = bookingData.totalPrice || taxInfo.totalAfterTax;

      // Only create payment link if there's an amount to pay
      if (totalAmount <= 0) return null;

      // Create a descriptive product name
      const vehicleDescriptions = bookingData.vehicles.map((v: any, index: number) => {
        if (v.type === 'car') {
          return `${v.type.toUpperCase()} #${index + 1}: ${v.package}`;
        } else {
          return `${v.type.toUpperCase()} #${index + 1}: ${v.services.join(', ')}`;
        }
      }).join(' | ');

      const productName = vehicleDescriptions || 'Detail Service';

      // Create a description with key details
      const description = `${formData.date} at ${formData.time} - ${formData.address.address}`;

      const paymentLinkPayload = {
        bookingId: bookingId,
        customerName: `${formData.firstName} ${formData.lastName}`,
        customerEmail: formData.email,
        customerPhone: formData.phone,
        amount: totalAmount,
        productName: productName,
        description: description,
        branch: formData.branch || 'lwr',
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      };

      const response = await fetch('/api/create-payment-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentLinkPayload)
      });

      if (!response.ok) {
        throw new Error('Failed to create payment link');
      }

      const result = await response.json();
      return result.paymentUrl;

    } catch (error) {
      console.error('Error creating payment link:', error);
      return null;
    }
  };

  // Update your handleBookingWithoutPayment function:
  const handleBookingWithoutPayment = async () => {
    setIsSubmitting(true);
    setCurrentStep(6);

    const submitBooking = async (retryCount = 0): Promise<void> => {
      const maxRetries = 5;
      
      try {
        const bookingData = {
          ...formData,
          address: formData.address.address,
          vehicles: vehicles.map((v) => ({
            ...v,
            pricingDetails: getVehiclePricingDetails(v),
          })),
          paymentStatus: "not_required",
          subtotal: taxInfo.totalBeforeTax,
          taxRate: taxInfo.taxRate,
          taxAmount: taxInfo.totalTax,
          totalPrice: taxInfo.totalAfterTax,
        };

        // Increase timeout for booking request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const response = await fetch("/api/bookings", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(bookingData),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error("Booking creation failed");
        }

        const bookingResult = await response.json();
        const bookingId = bookingResult.bookingId || bookingResult.id;

        // Create payment link after successful booking (only if amount > 0)
        let paymentUrl = null;
        if (bookingId && taxInfo.totalAfterTax > 0) {
          paymentUrl = await createPaymentLink(bookingData, bookingId);
        }

        // Store payment URL for your existing email system to use
        // You'll modify your /api/bookings endpoint to include this in the confirmation email
        if (paymentUrl) {
          // You can either:
          // 1. Pass paymentUrl to your existing email in the booking API, OR
          // 2. Store it and let your booking API call the payment link creation
          console.log('Payment URL created:', paymentUrl);
        }

        setCurrentStep(7);
      } catch (error) {
        console.error(`Booking attempt ${retryCount + 1} failed:`, error);
        
        if (retryCount < maxRetries - 1) {
          console.log(`Retrying booking... (${retryCount + 2}/${maxRetries})`);
          const delay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff, max 5s
          await new Promise(resolve => setTimeout(resolve, delay));
          return submitBooking(retryCount + 1);
        } else {
          throw new Error(`Booking failed after ${maxRetries} attempts`);
        }
      }
    };

    try {
      await submitBooking();
    } catch (error) {
      console.error("Booking error:", error);
      alert("Failed to create booking");
      setCurrentStep(4);
    } finally {
      setIsSubmitting(false);
    }
  };


  const router = useRouter();


  const [expandedVehicleIndex, setExpandedVehicleIndex] = useState<number | null>(null);
  const [showVehicleTypeModal, setShowVehicleTypeModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [vehicles, setVehicles] = useState<VehicleBooking[]>([]);
  const [selectedType, setSelectedType] = useState<VehicleType>("car");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isCancellation, setIsCancellation] = useState(false);
  
  // Database configuration state
  const [dbVehicleConfig, setDbVehicleConfig] = useState<VehicleConfig | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(true);
  
  const [formData, setFormData] = useState<FormData>({
    vehicles: [],
    date: "",
    time: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: { address: "" },
    petHair: null,
    petHairLevel: null,

    calculatedPrice: 0,
    nameOnCard: "",
    cardPhone: "",
    cardEmail: "",
    waterElectricity: "no",  // Default to "no"
    garageParking: "no",     // Default to "no"
    bathroomAvailable: "no", // Default to "no"
    howFound: "other",        // Default to "other"

    isRecurring: false,
    frequency: "",
    plan: "Once",
    notes: "",
    termsAgreed: false,
    stripeCustomerId: "",
    stripeName: "",
    stripeEmail: "",
    stripePhone: "",
    stripeAddress: {
      line1: "",
      city: "",
      state: "",
      postal_code: "",
      country: "US",
    },

  });

  const phrases = [
    "Processing your booking...",
    "Securing your time slot...",
    "Confirming availability...",
    "Setting up your appointment...",
    "Almost done...",
  ];
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);

  const isStep4Valid = () => {
    return (
      formData.firstName.trim() !== "" &&
      formData.lastName.trim() !== "" &&
      formData.phone.trim() !== "" &&
      formData.waterElectricity !== "" &&
      formData.garageParking !== "" &&
      formData.bathroomAvailable !== "" &&
      formData.howFound !== "" &&
      formData.termsAgreed
    );
  };
  const [closestBranch, setClosestBranch] = useState<BranchDetails | null>(null);


  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const [addressError, setAddressError] = useState<string | null>(null);
  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };
  const isSubmitted = useRef(false);


  useEffect(() => {
    const addressParts = formData.address.address.split(",").map(part => part.trim());
    const stateZip = addressParts[2] ? addressParts[2].split(/\s+/) : [];
    const state = stateZip[0] || '';
    const postal_code = stateZip.slice(1).join(' ') || '';

    setFormData((prev) => ({
      ...prev,
      stripeName: `${prev.firstName} ${prev.lastName}`.trim(),
      stripeEmail: validateEmail(prev.email) ? prev.email : "",
      stripePhone: prev.phone,
      stripeAddress: {
        line1: addressParts[0] || "",
        city: addressParts[1] || "",
        state: state,
        postal_code: postal_code,
        country: "US", // Country is set from dropdown, no need to include in address
      },
    }));
  }, [formData.firstName, formData.lastName, formData.email, formData.phone, formData.address]);

  // Load database configuration on component mount
  useEffect(() => {
    const loadDatabaseConfig = async () => {
      try {
        setIsLoadingConfig(true);
        const branch = closestBranch?.branch || formData.branch || 'lwr';
        
        // Get packages, addons, and sizes for all vehicle types
        const [carPackages, carAddons, carSizes, boatServices, rvServices] = await Promise.all([
          getBranchPackages(branch, 'car'),
          getBranchAddons(branch, 'car'),
          getBranchVehicleSizes(branch),
          getBranchPackages(branch, 'boat'),
          getBranchPackages(branch, 'rv')
        ]);

        console.log('Database data retrieved:', {
          carPackages: carPackages.length,
          carAddons: carAddons.length, 
          carSizes: carSizes.length,
          boatServices: boatServices.length,
          rvServices: rvServices.length,
          branch
        });

        // Create VehicleConfig structure from database data
        const dbConfig: VehicleConfig = {
          car: {
            sizes: carSizes.length > 0 ? carSizes.map(s => s.name) : vehicleConfig.car.sizes,
            sizeMultipliers: carSizes.length > 0 ? carSizes.map(s => s.baseMultiplier) : vehicleConfig.car.sizeMultipliers,
            packages: carPackages.length > 0 ? carPackages.map(p => ({
              name: p.name,
              price: p.basePrice,
              images: p.images || [],
              branches: p.availableBranches || ['lwr', 'w-stl', 'dvr', 'ny']
            })) : vehicleConfig.car.packages,
            addons: carAddons.length > 0 ? carAddons.map(a => ({
              name: a.name,
              price: a.basePrice,
              hasQuantity: a.hasQuantity,
              description: a.description || '',
              images: a.images || [],
              branches: a.availableBranches || ['lwr', 'w-stl', 'dvr', 'ny']
            })) : vehicleConfig.car.addons
          },
          boat: {
            services: boatServices.length > 0 ? boatServices.map(s => ({
              name: s.name,
              price: s.basePrice,
              description: s.description || '',
              images: s.images || [],
              branches: s.availableBranches || ['lwr', 'w-stl']
            })) : vehicleConfig.boat.services,
            addons: [] // Boats don't have addons currently
          },
          rv: {
            services: rvServices.length > 0 ? rvServices.map(s => ({
              name: s.name,
              price: s.basePrice,
              description: s.description || '',
              images: s.images || [],
              branches: s.availableBranches || ['lwr', 'w-stl', 'dvr', 'ny']
            })) : vehicleConfig.rv.services,
            addons: [] // RVs don't have addons currently
          }
        };

        setDbVehicleConfig(dbConfig);
        console.log('Database connected successfully - Using database pricing:', {
          branch,
          interiorPrice: dbConfig.car.packages.find(p => p.name === 'Interior Detailing')?.price,
          carPackagesCount: dbConfig.car.packages.length,
          carAddonsCount: dbConfig.car.addons.length,
          carSizesCount: dbConfig.car.sizes.length,
          boatServicesCount: dbConfig.boat.services.length,
          rvServicesCount: dbConfig.rv.services.length,
          usingFallbackSizes: carSizes.length === 0,
          usingFallbackCarPackages: carPackages.length === 0,
          usingFallbackCarAddons: carAddons.length === 0,
          usingFallbackBoatServices: boatServices.length === 0,
          usingFallbackRvServices: rvServices.length === 0
        });
      } catch (error) {
        console.warn('❌ Database connection failed - Using static fallback pricing:', error);
        console.log('📊 Fallback pricing info:', {
          interiorPrice: vehicleConfig.car.packages.find(p => p.name === 'Interior Detailing')?.price,
          packagesCount: vehicleConfig.car.packages.length,
          addonsCount: vehicleConfig.car.addons.length,
          sizesCount: vehicleConfig.car.sizes.length
        });
        // Keep dbVehicleConfig as null to use fallback
      } finally {
        setIsLoadingConfig(false);
      }
    };

    loadDatabaseConfig();
  }, [closestBranch?.branch, formData.branch]);

  // Log pricing source whenever vehicles are displayed or change
  useEffect(() => {
    if (vehicles.length > 0) {
      const config = getCurrentVehicleConfig();
      const dataSource = dbVehicleConfig ? 'DATABASE' : 'STATIC FALLBACK';
      
      console.log('\nBOOKING SUMMARY - Vehicle List Updated:');
      console.log(`Data Source: ${dataSource}`);
      
      // Show comprehensive vehicle information
      vehicles.forEach((vehicle, index) => {
        const vehicleType = vehicle.type; // Use 'type' instead of 'vehicleType'
        console.log(`\nVehicle #${index + 1} (${vehicleType.toUpperCase()}):`);
        
        if (vehicleType === 'car') {
          console.log(`  Package: ${vehicle.package || 'Not selected'}`);
          console.log(`  Size: ${vehicle.size || 'Not selected'}`);
          console.log(`  Total Cost: $${vehicle.totalPrice || 0}`);
          console.log(`  Add-ons: ${vehicle.addons?.length || 0} selected`);
          if (vehicle.addons && vehicle.addons.length > 0) {
            vehicle.addons.forEach((addon: any) => {
              console.log(`    - ${addon.name}: quantity ${addon.quantity || 1}`);
            });
          }
        } else if (vehicleType === 'boat' || vehicleType === 'rv') {
          console.log(`  Services: ${vehicle.services?.length || 0} selected`);
          console.log(`  Total Cost: $${vehicle.totalPrice || 0}`);
          if (vehicle.services && vehicle.services.length > 0) {
            vehicle.services.forEach((service: string) => {
              console.log(`    - ${service}`);
            });
          }
        }
      });
      
      // Show available pricing data based on selected vehicle type
      const selectedVehicleType = vehicles.length > 0 ? vehicles[0].type : 'car';
      console.log('\nAVAILABLE PRICING DATA:');
      
      if (selectedVehicleType === 'car') {
        const interiorPrice = config.car.packages.find(p => p.name === 'Interior Detailing')?.price;
        console.log(`  Car Interior Detailing: $${interiorPrice}`);
        console.log(`  Car Packages: ${config.car.packages.length} available`);
        console.log(`  Car Add-ons: ${config.car.addons.length} available`);
        console.log(`  Car Sizes: ${config.car.sizes.length} available`);
      } else if (selectedVehicleType === 'boat') {
        console.log(`  Boat Services: ${config.boat?.services?.length || 0} available`);
        if (config.boat?.services) {
          config.boat.services.forEach((service: any) => {
            console.log(`    - ${service.name}: $${service.price}`);
          });
        }
      } else if (selectedVehicleType === 'rv') {
        console.log(`  RV Services: ${config.rv?.services?.length || 0} available`);
        if (config.rv?.services) {
          config.rv.services.forEach((service: any) => {
            console.log(`    - ${service.name}: $${service.price}`);
          });
        }
      }
      
      console.log(`\nTotal Vehicles: ${vehicles.length}`);
      const totalCost = vehicles.reduce((sum, v) => sum + (v.totalPrice || 0), 0);
      console.log(`Grand Total: $${totalCost.toFixed(2)}`);
    }
  }, [vehicles, dbVehicleConfig]);

  // Function to get current vehicle config (database or fallback)
  const getCurrentVehicleConfig = (): VehicleConfig => {
    if (dbVehicleConfig) {
      // Only log when actually called, not repeatedly
      return dbVehicleConfig;
    } else {
      // Only log when actually called, not repeatedly
      return vehicleConfig;
    }
  };

  useEffect(() => {
    const calculateTaxForBooking = async () => {
      const subtotal = vehicles.reduce((sum, v) => sum + v.totalPrice, 0); // Moved outside try-catch

      if (formData.address.street && formData.address.city && formData.address.state && formData.address.postalCode) {
        try {
          const taxData = {
            addressStreet: formData.address.street,
            addressCity: formData.address.city,
            addressState: formData.address.state,
            addressPostal: formData.address.postalCode,
            addressCountry: "US",
            amount: subtotal,
          };

          const taxResult = await calculateTax(taxData);

          if (!taxResult.error) {
            const taxRate = parseFloat(taxResult.taxRate);
            const taxAmount = parseFloat(taxResult.totalTax);
            const totalAmount = subtotal + taxAmount;

            setTaxInfo({
              totalBeforeTax: subtotal,
              taxRate: taxRate,
              totalTax: taxAmount,
              totalAfterTax: totalAmount,
            });
          } else {
            setTaxInfo((prev) => ({
              ...prev,
              totalBeforeTax: subtotal,
              error: taxResult.error,
            }));
          }
        } catch (error) {
          console.error("Tax calculation failed:", error);
          setTaxInfo((prev) => ({
            ...prev,
            totalBeforeTax: subtotal, // Now accessible
            error: "Failed to calculate tax",
          }));
        }
      }
    };

    calculateTaxForBooking();
  }, [vehicles, formData.address]);

  useEffect(() => {
    if (currentStep === 6) {
      const interval = setInterval(() => {
        setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [currentStep]);

  // Auto-scroll to top when step changes
  useEffect(() => {
    // Use setTimeout to ensure DOM has rendered before scrolling
    setTimeout(() => {
      // Remove focus from any active input to prevent mobile keyboard popup
      if (document.activeElement && document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }
      scrollContainerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
    }, 50);
  }, [currentStep]);

  const handleCancel = () => {
    setIsCancellation(true); // Indicate this is a cancellation
    setCurrentStep(8);       // Immediately show slide 7

    // Check if no information has been entered
    const isNoInfo = vehicles.length === 0 &&
      formData.address.address === "" &&
      formData.email === "";

    // Prepare data based on whether information exists
    const cancelData = isNoInfo
      ? {
        message: "canceled with no information",
        timestamp: new Date().toISOString(),
        vehicles: []
      }
      : {
        ...formData,
        vehicles: vehicles.map((vehicle) => ({
          ...vehicle,
          pricingDetails: getVehiclePricingDetails(vehicle),
        })),
        timestamp: new Date().toISOString()
      };

    // Send data to the backend (fire and forget)
    fetch('/api/cancel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(cancelData),
    }).catch(error => {
      console.error('Cancel data save error:', error);
    });

    // Close the popup after 3 seconds
    setTimeout(() => {
      onClose();
    }, 3000);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    setFormData((prev) => ({
      ...prev,
      email,
      cardEmail: email, // Sync cardEmail with email
    }));
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address.");
    } else {
      setEmailError(null);
    }
  };


  const isStep1Valid = () => {
    return formData.address.address.trim().length > 0;
  };
  const isStep3Valid = () => {
    const dateSelected = formData.date !== "" && formData.time !== "";
    if (!formData.isRecurring) {
      return dateSelected;
    } else {
      return dateSelected && formData.frequency !== "";
    }
  };

  const handleSlotSelect = (date: string, time: string) => {
    setFormData(prev => ({
      ...prev,
      date,
      time
    }));
  };


  const [taxInfo, setTaxInfo] = useState<TaxInfo>({
    totalBeforeTax: 0,
    taxRate: 0,
    totalTax: 0,
    totalAfterTax: 0,
  });


  const addVehicle = (type: VehicleType) => {
    console.log(`🚗 Adding new ${type.toUpperCase()} vehicle - Current pricing source:`, 
      dbVehicleConfig ? 'DATABASE' : 'STATIC FALLBACK');
    
    const currentConfig = getCurrentVehicleConfig();
    console.log(`💰 ${type.toUpperCase()} pricing info:`, {
      interiorPrice: currentConfig.car.packages.find(p => p.name === 'Interior Detailing')?.price,
      source: dbVehicleConfig ? 'Database' : 'Static Fallback'
    });
    
    let newVehicle: VehicleBooking;

    if (type === "car") {
      newVehicle = {
        type: "car",
        year: "",
        make: "",
        model: "",

        size: getCurrentVehicleConfig().car.sizes[0],
        package: getCurrentVehicleConfig().car.packages[0].name,
        petHair: null, // Add this
        petHairLevel: null, // Add this
        addons: [],
        basePrice: 0,
        addonsPrice: 0,
        totalPrice: 0,
      };
    } else if (type === "boat") {
      newVehicle = {
        type: "boat",
        services: [],
        length: 0,
        addons: [],
        basePrice: 0,
        addonsPrice: 0,
        totalPrice: 0,
      };
    } else {
      newVehicle = {
        type: "rv",
        services: [],
        length: 0,
        addons: [],
        basePrice: 0,
        addonsPrice: 0,
        totalPrice: 0,
      };
    }

    setVehicles([...vehicles, newVehicle]);
    setExpandedVehicleIndex(vehicles.length);
    setShowVehicleTypeModal(false);
  };

  const removeVehicle = (index: number) => {
    setVehicles(vehicles.filter((_, i) => i !== index));
    if (expandedVehicleIndex === index) {
      setExpandedVehicleIndex(null);
    }
  };

  const calculateVehiclePrice = (vehicle: VehicleBooking): VehicleBooking => {
    if (vehicle.type === "car") {
      const carConfig = getCurrentVehicleConfig().car;
      const sizeIndex = carConfig.sizes.indexOf(vehicle.size);
      const sizeMultiplier = carConfig.sizeMultipliers[sizeIndex] || 1;
      const packagePrice = carConfig.packages.find(p => p.name === vehicle.package)?.price || 0;
      
      console.log(`💵 Calculating price for ${vehicle.package}:`, {
        basePrice: packagePrice,
        sizeMultiplier: sizeMultiplier,
        vehicleSize: vehicle.size,
        finalPrice: packagePrice * sizeMultiplier,
        source: dbVehicleConfig ? 'Database' : 'Static Fallback'
      });
      
      const basePrice = packagePrice * sizeMultiplier;
      const addonsPrice = vehicle.addons.reduce(
        (sum, a) => sum + ((carConfig.addons.find(ad => ad.name === a.name)?.price || 0) * a.quantity),
        0
      );

      let petHairPrice = 0;
      if (vehicle.petHair === 'yes' && vehicle.petHairLevel) {
        switch (vehicle.petHairLevel) {
          case 1: petHairPrice = 75; break;
          case 2: petHairPrice = 100; break;
          case 3: petHairPrice = 150; break;
        }
      }

      return {
        ...vehicle,
        basePrice,
        addonsPrice,
        totalPrice: basePrice + addonsPrice + petHairPrice
      };

    } else {
      const config = vehicleConfig[vehicle.type];

      const servicesPrice = vehicle.services.reduce(
        (sum, s) => sum + ((config.services.find(srv => srv.name === s)?.price || 0) * (vehicle.length ?? 0)),
        0
      );

      const addonsPrice = vehicle.addons.reduce(
        (sum, a) => {
          const addon = config.addons.find(ad => ad.name === a.name);
          let price = addon?.price || 0;

          if (vehicle.type === "rv" && a.name === "roof") {
            price = 5 * (vehicle.length || 0);
          }

          return sum + (price * a.quantity);
        },
        0
      );

      return {
        ...vehicle,
        basePrice: Number(servicesPrice),
        addonsPrice: Number(addonsPrice),
        totalPrice: Number(servicesPrice + addonsPrice)
      };
    }
  };


  const getVehiclePricingDetails = (vehicle: VehicleBooking): { description: string; amount: number }[] => {
    const details: { description: string; amount: number }[] = [];

    if (vehicle.type === "car") {
      const carConfig = getCurrentVehicleConfig().car;
      const packageConfig = carConfig.packages.find(p => p.name === vehicle.package);
      if (packageConfig) {
        const sizeIndex = carConfig.sizes.indexOf(vehicle.size);
        const sizeMultiplier = carConfig.sizeMultipliers[sizeIndex] || 1;
        const basePackagePrice = packageConfig.price * sizeMultiplier;
        details.push({
          description: `Package: ${vehicle.package} for ${vehicle.size}`,
          amount: basePackagePrice
        });
      }
      if (vehicle.petHair === 'yes' && vehicle.petHairLevel) {
        const petHairPrices = { 1: 75, 2: 100, 3: 150 };
        const petHairPrice = petHairPrices[vehicle.petHairLevel] || 0;
        details.push({
          description: `Pet Hair Removal (Level ${vehicle.petHairLevel})`,
          amount: petHairPrice
        });
      }
      vehicle.addons.forEach(addon => {
        const addonConfig = carConfig.addons.find(a => a.name === addon.name);
        if (addonConfig) {
          const addonTotal = addonConfig.price * addon.quantity;
          details.push({
            description: `${addon.name} × ${addon.quantity}`,
            amount: addonTotal
          });
        }
      });
    } else {
      const config = vehicleConfig[vehicle.type];
      vehicle.services.forEach(service => {
        const serviceConfig = config.services.find(s => s.name === service);
        if (serviceConfig) {
          const serviceTotal = serviceConfig.price * (vehicle.length || 0);
          details.push({
            description: `${service} (${vehicle.length}ft)`,
            amount: serviceTotal
          });
        }
      });
      vehicle.addons.forEach(addon => {
        const addonConfig = config.addons.find(a => a.name === addon.name);
        if (addonConfig) {
          let addonTotal;
          if (vehicle.type === "rv" && addon.name === "roof") {
            const pricePerFoot = 5;
            addonTotal = pricePerFoot * (vehicle.length || 0) * addon.quantity;
            details.push({
              description: `Roof Addon (${vehicle.length}ft) × ${addon.quantity}`,
              amount: addonTotal
            });
          } else {
            addonTotal = addonConfig.price * addon.quantity;
            details.push({
              description: `${addon.name} × ${addon.quantity}`,
              amount: addonTotal
            });
          }
        }
      });
    }
    return details;
  };
  const updateVehicle = (index: number, updates: Partial<VehicleBooking>) => {
    const updatedVehicles = vehicles.map((vehicle, i) => {
      if (i === index) {
        let updatedVehicle: VehicleBooking;

        if (vehicle.type === "car") {
          updatedVehicle = {
            ...vehicle,
            ...updates,
            type: "car",
          } as CarBooking;
        } else if (vehicle.type === "boat") {
          updatedVehicle = {
            ...vehicle,
            ...updates,
            type: "boat",
          } as BoatBooking;
        } else {
          updatedVehicle = {
            ...vehicle,
            ...updates,
            type: "rv",
          } as RVBooking;
        }

        updatedVehicle = calculateVehiclePrice(updatedVehicle);
        return updatedVehicle;
      }
      return vehicle;
    });

    setVehicles(updatedVehicles);
    updateTotalPrice(updatedVehicles);
  };




  const updateTotalPrice = (vehicles: VehicleBooking[]) => {
    const subtotal = vehicles.reduce((sum, v) => sum + v.totalPrice, 0);

    setFormData(prev => ({
      ...prev,
      vehicles,
    }));

    // Remove .toFixed() to keep as number
    setTaxInfo(prev => ({
      ...prev,
      totalBeforeTax: subtotal, // Correct: number instead of string
    }));
  };
  const containerRef = useRef<HTMLDivElement>(null);
  const prevVehiclesLength = useRef(0);

  useEffect(() => {
    if (vehicles.length > prevVehiclesLength.current) {
      containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
    }
    prevVehiclesLength.current = vehicles.length;
  }, [vehicles.length]);

  useEffect(() => {
    if (vehicles.length > prevVehiclesLength.current) {
      containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
    }

    if (expandedVehicleIndex !== null) {
      const element = document.getElementById(`vehicle-${expandedVehicleIndex}`);
      element?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    prevVehiclesLength.current = vehicles.length;
  }, [vehicles.length, expandedVehicleIndex]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-0 z-50">
      {showVehicleTypeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white p-8 rounded-xl space-y-4 w-80">
            <h3 className="text-xl font-bold text-blue-900">Select Vehicle Type</h3>
            <div className="grid grid-cols-1 gap-3">
              {(["car", "boat", "rv"] as VehicleType[]).filter(type => {
                const config = vehicleConfig[type];

                // Proper type narrowing with type guards
                if (type === "car") {
                  // Use type assertion for car config
                  return (config as VehicleConfig['car']).packages.some((pkg) =>
                    pkg.branches.includes(formData.branch || "lwr")
                  );
                } else {
                  // Type assertion for boat/rv config
                  const serviceConfig = config as VehicleConfig['boat' | 'rv'];
                  return serviceConfig.services.some((service) =>
                    service.branches.includes(formData.branch || "lwr")
                  );
                }
              }).map(type => (
                <button
                  key={type}
                  onClick={() => addVehicle(type)}
                  className="p-4 bg-blue-50 rounded-lg hover:bg-blue-100 text-blue-900 text-left"
                >
                  <div className="font-medium">{type.charAt(0).toUpperCase() + type.slice(1)}</div>
                  <div className="text-sm text-gray-600">
                    {type === "car" ? "Sedans, SUVs, Trucks" :
                      type === "boat" ? "Powerboats, Sailboats" :
                        "Motorhomes, Trailers"}
                  </div>
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowVehicleTypeModal(false)}
              className="w-full mt-4 py-2 text-gray-600 hover:text-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      <div className="flex flex-col w-full h-full sm:h-[95vh] bg-white shadow-lg rounded-none sm:rounded-lg border-0 sm:border border-blue-100 backdrop-blur-sm sm:mx-4 relative z-50 sm:max-w-3xl">
        {/* Top Section - Skinnier */}
        {currentStep === 6 && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 animate-gradient-x">
            <p className="text-white text-lg font-semibold">{phrases[currentPhraseIndex]}</p>
          </div>
        )}
        <div className="flex justify-between items-center px-2 py-1">
          <div className="w-3/4 h-0.5 bg-blue-100 rounded-full">
            <div
              className="h-2 bg-blue-600 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / 6) * 100}%` }}
            />
          </div>
          <button
            onClick={handleCancel}
            className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
            disabled={isSubmitting} // Add disabled attribute
          >
            Cancel
          </button>
        </div>

        {/* Content Area */}
        <div ref={scrollContainerRef} className={`flex-1 overflow-y-auto py-4 ${currentStep === 2 ? 'px-0 sm:px-8' : 'px-4 sm:px-8'}`}>          {currentStep === 1 && (
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-blue-900">Welcome to Detail On The Go!</h2>
            <h3 className="text-lg font-normal text-blue-700">Enter your location and email below to view pricing, packages, and availability in your area.</h3>


            {formData.address.address && (
              <div className="p-3 bg-blue-50 rounded-lg animate-fade-in">
                <p className="text-blue-900 font-semibold">Selected Location:</p>
                <p className="text-blue-900">{formData.address.address}</p>
              </div>
            )}

            {/* Detailer Information */}
            {closestBranch && (
              <div className="p-3 bg-blue-50 rounded-lg space-y-1 animate-slide-up">
                <p className="text-blue-900 font-semibold">Your Detailer's Information:</p>
                <p className="text-blue-900">😊 {closestBranch.employeeName}</p>
                <p className="text-blue-900">📞 {closestBranch.businessNumber}</p>
              </div>
            )}

            <div className="grid grid-cols-1 gap-4">
              {/* Email Input */}
              <div className="space-y-2">
                <input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleEmailChange}
                  className="w-full p-3 border-2 border-blue-600 rounded-lg 
            bg-white text-gray-900 placeholder-blue-600/80
            focus:border-blue-700 focus:ring-2 focus:ring-blue-500
            transition-all duration-200 hover:border-blue-500"
                  aria-invalid={!!emailError}
                />
                {emailError && (
                  <p className="text-red-700 text-sm mt-1 flex items-center gap-1 font-medium">
                    <svg className="w-4 h-4 shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {emailError}
                  </p>
                )}
              </div>

              {/* Address Search */}
              <div className="relative">
                <AddressInput2
                  onAddressSelected={(address) => {
                    const formatted = `${address.street}, ${address.city}, ${address.state} ${address.postalCode}`;
                    setFormData(prev => ({
                      ...prev,
                      address: {
                        address: formatted,
                        ...address
                      }
                    }));
                  }}
                  onBranchFound={(branch) => {
                    setFormData(prev => ({
                      ...prev,
                      branch: branch.branch as Branch
                    }));
                    setClosestBranch(branch);
                  }}
                />
                <div className="absolute inset-y-0 right-3 flex items-center">
                  <span className="text-gray-400">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </span>
                </div>
              </div>
            </div>


          </div>
        )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-blue-900">Your Vehicles</h2>
              <button
                onClick={() => {
                  // Blur button to prevent mobile keyboard
                  if (document.activeElement && document.activeElement instanceof HTMLElement) {
                    document.activeElement.blur();
                  }
                  setShowVehicleTypeModal(true);
                }}
                className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                ＋ Add Another Car, Boat or RV
              </button>
              <div className="space-y-4 mt-4 vehicle-list-container" style={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }}>
                {vehicles.map((vehicle, index) => (
                  <div
                    key={index}
                    className={`relative z-40 border rounded-xl transition-all duration-300 ${expandedVehicleIndex === index
                      ? 'border-blue-600 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-400 cursor-pointer'
                      }`}
                    onClick={() => {
                      if (expandedVehicleIndex !== index) {
                        setExpandedVehicleIndex(index);
                      }
                    }}
                  >
                    <div className="px-2 py-4 sm:p-4 flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <span className="font-semibold text-blue-900">
                          {vehicle.type.toUpperCase()} #{index + 1}
                        </span>
                        <span className="text-blue-600">
                          ${vehicle.totalPrice.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center gap-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeVehicle(index);
                          }}
                          className="bg-red-600 text-white px-3 py-1 rounded-lg text-lg hover:bg-red-700 transition-colors"
                        >
                          Remove
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setExpandedVehicleIndex(
                              expandedVehicleIndex === index ? null : index
                            );
                          }}
                          className="bg-blue-600 text-white px-3 py-1 rounded-lg text-lg hover:bg-blue-700 transition-colors"
                        >
                          −
                        </button>
                      </div>
                    </div>
                    {expandedVehicleIndex === index && (
                      <div className="px-2 py-4 sm:p-4 border-t border-blue-100">
                        <VehicleForm
                          index={index}
                          vehicle={vehicle}
                          updateVehicle={updateVehicle}
                          removeVehicle={() => removeVehicle(index)}
                          branch={formData.branch || "lwr"}
                          vehicleConfig={getCurrentVehicleConfig()}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-4 px-2 sm:px-4">
              <h2 className="text-xl sm:text-2xl font-bold text-blue-900 px-2 sm:px-0">
                Schedule & Service Frequency
              </h2>

              <div className="border-0 sm:border p-0 sm:p-4 rounded-none sm:rounded-lg -mx-4 sm:mx-0">
                <SchedulingPage
                  branch={formData.branch || "lwr"}
                  onSelectSlot={handleSlotSelect}
                  selectedDate={formData.date}
                  selectedTime={formData.time}
                />

                {formData.date && formData.time && (
                  <div className="mt-4 p-2 sm:p-3 bg-blue-100 rounded-lg text-center sm:text-left text-sm font-medium text-blue-900">
                    Selected: {formData.date} at {formData.time}
                  </div>
                )}
              </div>

              <div className="mt-6">
                <div className="bg-blue-900 text-white p-6 rounded-xl space-y-4">
                  <h3 className="text-xl font-bold text-center">
                    If we do an amazing job for you, how soon would you like us to come back?
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {(() => {
                      const basePrice = formData.vehicles[0]?.basePrice || 0;
                      const normalPrice = basePrice; // Keep as number for calculations

                      return [
                        {
                          freq: "Never" as const,
                          label: "One-Time Detail",
                          price: normalPrice,
                          discount: 0,
                          isMembership: false
                        },
                        {
                          freq: "Every Month" as const,
                          label: "Monthly Membership",
                          price: 99,
                          discount: normalPrice - 99,
                          isMembership: true
                        },
                        {
                          freq: "Every 2 Months" as const,
                          label: "Bi-Monthly",
                          price: 160,
                          discount: normalPrice - 160,
                          isMembership: true
                        },
                        {
                          freq: "Every 3 Months" as const,
                          label: "Quarterly",
                          price: 200,
                          discount: normalPrice - 200,
                          isMembership: true
                        },
                        {
                          freq: "Every 4 Months" as const,
                          label: "4-Month Plan",
                          price: normalPrice,
                          discount: 0,
                          isMembership: false
                        },
                        {
                          freq: "Once a year" as const,
                          label: "Annual",
                          price: normalPrice,
                          discount: 0,
                          isMembership: false
                        }
                      ].map((option) => {
                        // Format numbers to currency strings
                        const priceDisplay = option.isMembership
                          ? `Starting at $${option.price.toFixed(2)}`
                          : basePrice > 0 ? `$${option.price.toFixed(2)}` : '';

                        const discountMessage = option.isMembership && basePrice > 0
                          ? option.discount > 0
                            ? `Save $${option.discount.toFixed(2)} per detail (${(
                              (option.discount / basePrice) * 100
                            ).toFixed(0)}% savings)`
                            : 'No discount'
                          : 'No discount';

                        return (
                          <button
                            key={option.freq}
                            onClick={() => {
                              const isRecurring = option.freq !== "Never";
                              setFormData(prev => ({
                                ...prev,
                                isRecurring,
                                frequency: isRecurring ? option.freq : ""
                              }));
                            }}
                            className={`p-4 text-left rounded-lg transition-all ${formData.frequency === option.freq
                              ? 'bg-blue-100 text-blue-900 border-2 border-blue-300'
                              : 'bg-white/10 hover:bg-white/20 border-2 border-transparent'
                              }`}
                          >
                            <div className="font-bold text-lg mb-1">{option.freq}</div>
                            <div className="text-sm opacity-90">{option.label}</div>
                            <div className="mt-2">
                              <span className="font-semibold text-blue-600">
                                {basePrice > 0 ? priceDisplay : "Add vehicle to see pricing"}
                              </span>
                              <div className="text-xs mt-1 opacity-75">
                                {basePrice > 0 ? discountMessage : " "}
                              </div>
                            </div>
                          </button>
                        );
                      });
                    })()}
                  </div>
                </div>
              </div>

              {currentStep === 3 && (
                <div className="mt-4">
                  {!formData.date && (
                    <p className="text-red-600">Please select a date and time.</p>
                  )}
                  {formData.isRecurring && !formData.frequency && (
                    <p className="text-red-600">Please select a frequency for recurring service.</p>
                  )}
                  {!formData.vehicles.length && (
                    <p className="text-yellow-600">Add a vehicle to see membership pricing</p>
                  )}
                </div>
              )}
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-blue-900">Step 3: All about you!</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* First & Last Name - Equal Width */}
                <div className="col-span-2">
                  <div className="w-full flex gap-4">
                    <input
                      type="text"
                      placeholder="First Name"
                      value={formData.firstName}
                      onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                      className="w-1/2 p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700"
                    />
                    <input
                      type="text"
                      placeholder="Last Name"
                      value={formData.lastName}
                      onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                      className="w-1/2 p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700"
                    />
                  </div>
                </div>


                {/* Phone & Email - Full Width */}
                <div className="col-span-2">
                  <input
                    type="tel"
                    placeholder="📱 Phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700"
                  />
                </div>
                <div className="col-span-2">
                  <input
                    type="email"
                    placeholder="📧 Email"
                    value={formData.email}
                    onChange={handleEmailChange}
                    className="w-full p-3 border border-blue-300 rounded-lg bg-white text-blue-900 placeholder-blue-700 opacity-100 font-semibold"
                  />
                </div>

                {/* Address with Edit Button */}
                <div className="relative col-span-2">
                  <input
                    type="text"
                    placeholder="🗺️ Location"
                    value={formData.address.address}  // <-- Access the address string
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        address: { ...formData.address, address: e.target.value },
                      })
                    }
                    className="w-full p-3 border border-blue-400 rounded-lg bg-gray-100 text-blue-900 cursor-not-allowed placeholder-blue-700 font-semibold"
                    disabled
                  />
                  <button
                    onClick={() => {
                      setCurrentStep(1);
                      // Delay scroll to ensure step change is complete
                      setTimeout(() => {
                        scrollContainerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
                      }, 100);
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-blue-800 hover:text-blue-900"
                    aria-label="Edit address"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 stroke-blue-800 hover:stroke-blue-900"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M11 4h6a2 2 0 012 2v6m-2 4H5a2 2 0 01-2-2V8m14-2L7 15m0 0l-2 2m2-2l2-2"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block mb-2 text-blue-900">
                    💧⚡ Are water & electricity available at the FRONT of your location?
                  </label>
                  <select
                    value={formData.waterElectricity}
                    onChange={(e) => setFormData({ ...formData, waterElectricity: e.target.value })}
                    className="p-3 border rounded-lg w-full bg-white text-blue-900"
                    required // Add required attribute
                  >
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
                <div>
                  <label className="block mb-2 text-blue-900">🏠 Garage or Covered Parking</label>
                  <select
                    value={formData.garageParking}
                    onChange={(e) =>
                      setFormData({ ...formData, garageParking: e.target.value })
                    }
                    className="p-3 border border-blue-300 rounded-lg w-full bg-white text-blue-900"
                  >
                    <option value="">Select an option</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
                <div>
                  <label className="block mb-2 text-blue-900">🚽 Bathroom Available?</label>
                  <select
                    value={formData.bathroomAvailable}
                    onChange={(e) =>
                      setFormData({ ...formData, bathroomAvailable: e.target.value })
                    }
                    className="p-3 border border-blue-300 rounded-lg w-full bg-white text-blue-900"
                  >
                    <option value="">Select an option</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
                <div>
                  <label className="block mb-2 text-blue-900">🧐 How'd You Find Us?</label>
                  <select
                    value={formData.howFound}
                    onChange={(e) => setFormData({ ...formData, howFound: e.target.value })}
                    className="p-3 border border-blue-300 rounded-lg w-full bg-white text-blue-900"
                  >
                    <option value="">Select an option</option>
                    <option value="google">Google</option>
                    <option value="facebook">Facebook</option>
                    <option value="instagram">Instagram</option>
                    <option value="referral">Referral</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>
              <div className="border-t pt-4">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Your Order:</h3>
                <div className="space-y-2">
                  <p className="text-blue-900">
                    <span className="font-medium">📅 🕓 Date & Time:</span> {formData.date}{' '}
                    {formData.time}
                  </p>
                  <p className="text-blue-900">
                    <span className="font-medium">🗺️ Location:</span> {formData.address.address}
                  </p>
                  <p className="text-blue-900">
                    <span className="font-medium">🔁 Plan:</span>
                    {formData.isRecurring ? `${formData.frequency} recurring` : 'Once'}
                  </p>
                </div>
                <div className="mt-4">
                  {vehicles.map((vehicle, index) => (
                    <div key={index} className="mb-4 border-b pb-2">
                      <h4 className="text-md font-semibold text-blue-900">
                        {vehicle.type.toUpperCase()} #{index + 1}
                      </h4>

                      {vehicle.type === 'car' && (
                        <>
                          <p className="text-blue-900">
                            <span className="font-medium">🚗 Vehicle:</span>
                            {vehicle.year} {vehicle.make} {vehicle.model}
                          </p>
                          {/* Package display */}
                          {vehicle.petHair === 'yes' && vehicle.petHairLevel && (
                            <p className="text-blue-900">
                              <span className="font-medium">🐾 Pet Hair Removal (Level {vehicle.petHairLevel}):</span>
                              ${{
                                1: 75,
                                2: 100,
                                3: 150
                              }[vehicle.petHairLevel]}+
                            </p>
                          )}
                          <p className="text-blue-900">
                            <span className="font-medium">📦 Package ({vehicle.size}):</span>
                            {vehicle.package} - ${vehicle.basePrice.toFixed(2)}
                          </p>
                          {vehicle.addons.length > 0 && (
                            <p className="text-blue-900">
                              <span className="font-medium">🏷️ Add-Ons:</span>
                              {vehicle.addons.map((addon) => (
                                <span key={addon.name}>
                                  {addon.name} ($
                                  {(getCurrentVehicleConfig().car.addons.find((a) => a.name === addon.name)?.price ||
                                    0) * addon.quantity}
                                  ) x {addon.quantity}
                                  {addon !== vehicle.addons[vehicle.addons.length - 1] && ', '}
                                </span>
                              ))}
                              - ${vehicle.addonsPrice.toFixed(2)}
                            </p>
                          )}
                        </>
                      )}
                      {(vehicle.type === 'boat' || vehicle.type === 'rv') && (
                        <>
                          {vehicle.services.length > 0 && (
                            <p className="text-blue-900">
                              <span className="font-medium">📦 Services ({vehicle.length}ft):</span>
                              {vehicle.services.map((service) => (
                                <span key={service}>
                                  {service} ($
                                  {(vehicleConfig[vehicle.type].services.find((s) => s.name === service)
                                    ?.price || 0) * (vehicle.length || 0)}
                                  )
                                  {service !== vehicle.services[vehicle.services.length - 1] && ', '}
                                </span>
                              ))}
                              - ${vehicle.basePrice.toFixed(2)}
                            </p>
                          )}
                          {vehicle.addons.length > 0 && (
                            <p className="text-blue-900">
                              <span className="font-medium">🏷️ Add-Ons:</span>
                              {vehicle.addons.map((addon) => (
                                <span key={addon.name}>
                                  {addon.name} ($
                                  {addon.name === 'roof' && vehicle.type === 'rv'
                                    ? 5 * (vehicle.length || 0)
                                    : vehicleConfig[vehicle.type].addons.find((a) => a.name === addon.name)
                                      ?.price || 0}
                                  x {addon.quantity})
                                  {addon !== vehicle.addons[vehicle.addons.length - 1] && ', '}
                                </span>
                              ))}
                              - ${vehicle.addonsPrice.toFixed(2)}
                            </p>
                          )}
                        </>
                      )}
                      <p className="text-blue-900 font-semibold">
                        Subtotal: ${vehicle.totalPrice.toFixed(2)}
                      </p>
                    </div>
                  ))}
                  <div className="space-y-2 mt-4">
                    {taxInfo.error ? (
                      <div className="text-red-600">
                        <p>⚠️ Tax calculation unavailable - verify your address</p>
                        <p className="text-blue-900">
                          <span className="font-medium">Services Total:</span> $
                          {taxInfo.totalBeforeTax.toFixed(2)}
                        </p>
                      </div>
                    ) : (
                      <>
                        <p className="text-blue-900">
                          <span className="font-medium">Services Total:</span> $
                          {taxInfo.totalBeforeTax.toFixed(2)}
                        </p>
                        {taxInfo.taxRate > 0 ? (
                          <>
                            <p className="text-blue-900">
                              <span className="font-medium">Sales Tax ({(taxInfo.taxRate).toFixed(2)}%):</span> $
                              {taxInfo.totalTax.toFixed(2)}
                            </p>
                            <p className="text-blue-900 font-semibold">
                              <span className="font-medium">Total:</span> $
                              {taxInfo.totalAfterTax.toFixed(2)}
                            </p>
                          </>
                        ) : (
                          <>
                            <p className="text-green-600">✅ No sales tax applies to this location</p>
                            <p className="text-blue-900 font-semibold">
                              <span className="font-medium">Total:</span> $
                              {taxInfo.totalBeforeTax.toFixed(2)}
                            </p>
                          </>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <label className="block mb-2 text-blue-900">
                  What should we keep in mind or prioritize for your detail?
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  className="p-3 border border-blue-300 rounded-lg w-full bg-white text-blue-900"
                  rows={3}
                />
              </div>
              <div className="flex items-start gap-2">
                <input
                  type="checkbox"
                  checked={formData.termsAgreed}
                  onChange={(e) => setFormData({ ...formData, termsAgreed: e.target.checked })}
                  className="mt-1"
                />
                <p className="text-sm text-gray-600">
                  I have read and agreed to the terms and conditions. In the event of inclement weather,
                  or lack of technicians availability, my appointment may need to be rescheduled.
                </p>
              </div>
            </div>
          )}



          {currentStep === 6 && (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <h2 className="text-2xl font-bold text-blue-900">Finalizing Your Booking</h2>
              <div className="text-center space-y-2">
                <img
                  src="/DOTG circle.png"
                  alt="Loading spinner"
                  className="animate-spin h-16 w-16 object-contain" // Increased size and added object-contain
                />
                <p className="text-blue-900">Securing your time slot</p>
                <p className="text-sm text-gray-600">This may take a few seconds...</p>
              </div>
            </div>
          )}
          {currentStep === 7 && (
            <div className="flex flex-col items-center justify-center h-full space-y-6 text-center px-4">
              <div className="text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-blue-900">Booking Confirmed! 🎉</h2>
              <div className="space-y-2">
                <p className="text-blue-900">
                  We've sent confirmation details to <span className="font-semibold">{formData.email}</span>
                </p>
                <p className="text-blue-900">
                  Please check your texts and email to confirm your appointment
                </p>
                <div className="pt-6">
                  <button
                    onClick={onClose}
                    className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          )}
          {currentStep === 8 && (
            <div className="flex flex-col items-center justify-center h-full space-y-6 text-center px-4">
              {isCancellation ? (
                <>
                  <div className="text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-blue-900">Booking Cancelled</h2>
                  <div className="space-y-2">
                    <p className="text-blue-900">
                      Your booking has been cancelled. If you change your mind, feel free to book again anytime.
                    </p>
                    <p className="text-sm text-gray-600">
                      This popup will close automatically in a few seconds.
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-blue-900">Booking Confirmed! 🎉</h2>
                  <div className="space-y-2">
                    <p className="text-blue-900">
                      We've sent confirmation details to <span className="font-semibold">{formData.email}</span>
                    </p>
                    <p className="text-blue-900">
                      Please check your texts and email to confirm your appointment
                    </p>
                    <div className="pt-6">
                      <button
                        onClick={onClose}
                        className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

        </div>

        {/* Bottom Navigation */}
        <div className="bg-white border-t border-blue-100 py-4 px-8">
          <div className="flex justify-between">
            <button
              onClick={() => {
                setCurrentStep(Math.max(1, currentStep - 1));
                // Delay scroll to ensure step change is complete
                setTimeout(() => {
                  scrollContainerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
                }, 100);
              }}
              disabled={currentStep === 1 || currentStep === 6 || isSubmitting}
              className="px-6 py-2 bg-blue-200 text-blue-900 rounded-lg disabled:opacity-50"
            >
              Back
            </button>
            {currentStep < 4 && (
              <button
                onClick={() => {
                  // Blur any focused input to prevent mobile keyboard
                  if (document.activeElement && document.activeElement instanceof HTMLElement) {
                    document.activeElement.blur();
                  }
                  setCurrentStep(currentStep + 1);
                  // Delay scroll to ensure step change is complete
                  setTimeout(() => {
                    scrollContainerRef.current?.scrollTo({ top: 0, behavior: "smooth" });
                  }, 100);
                }}
                disabled={
                  (currentStep === 1 && !isStep1Valid()) ||
                  (currentStep === 3 && !isStep3Valid()) ||
                  isSubmitting
                }
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Next
              </button>
            )}
            {currentStep === 4 && (
              <button
                onClick={handleBookingWithoutPayment}
                disabled={!isStep4Valid() || isSubmitting}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Reserve Appointment
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
const Slideshow = ({ images }: { images: string[] }) => {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % images.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [images.length]);

  return (
    <div className="relative h-48 overflow-hidden rounded-lg">
      {images.map((img, index) => (
        <div
          key={img}
          className={`absolute inset-0 transition-opacity duration-1000 ${index === activeIndex ? 'opacity-100' : 'opacity-0'
            }`}
        >
          <img
            src={img}
            alt="Service example"
            className="w-full h-full object-cover"
          />
        </div>
      ))}
      <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1">
        {images.map((_, index) => (
          <button
            key={index}
            className={`w-2 h-2 rounded-full ${index === activeIndex ? 'bg-blue-600' : 'bg-white/50'
              }`}
            onClick={() => setActiveIndex(index)}
          />
        ))}
      </div>
    </div>
  );
};

const VehicleForm: React.FC<VehicleFormProps> = ({ index, vehicle, updateVehicle, removeVehicle, branch, vehicleConfig }) => {
  const config = vehicleConfig[vehicle.type] as VehicleConfig['boat'] | VehicleConfig['rv'];


  const getSizeMultiplier = () => {
    if (vehicle.type !== "car") return 1;
    const sizeIndex = vehicleConfig.car.sizes.indexOf(vehicle.size);
    return vehicleConfig.car.sizeMultipliers[sizeIndex] || 1;
  };
  const [showPopup, setShowPopup] = useState(false);
  const [popupContent, setPopupContent] = useState<{
    title: string;
    images: string[];
    warning?: string;
  }>({
    title: '',
    images: []
  });

  return (
    <div className="bg-gray-50 px-0 py-6 sm:p-6 rounded-xl border border-blue-100 relative">
      <h3 className="text-lg font-semibold mb-4 text-blue-900">
        {vehicle.type.toUpperCase()} Details
      </h3>

      {vehicle.type === "car" ? (

        <div className="space-y-6">
          {/* Existing car form code remains unchanged */}
          <div className="grid grid-cols-3 gap-4 mt-4">
            <input
              type="text"
              placeholder="Year"
              value={vehicle.year}
              onChange={(e) => updateVehicle(index, { year: e.target.value })}
              className="p-2 border rounded-lg w-full bg-white text-blue-900"
            />
            <input
              type="text"
              placeholder="Make"
              value={vehicle.make}
              onChange={(e) => updateVehicle(index, { make: e.target.value })}
              className="p-2 border rounded-lg w-full bg-white text-blue-900"
            />
            <input
              type="text"
              placeholder="Model"
              value={vehicle.model}
              onChange={(e) => updateVehicle(index, { model: e.target.value })}
              className="p-2 border rounded-lg w-full bg-white text-blue-900"
            />
          </div>
          <div>
            <label className="block mb-2 text-blue-900">Vehicle Size</label>
            <select
              value={vehicle.size}
              onChange={(e) => updateVehicle(index, { size: e.target.value })}
              className="p-2 border rounded-lg w-full bg-white text-blue-900"
            >
              {vehicleConfig.car.sizes.map((size: string) => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-2 text-blue-900">
              🐾 Does this vehicle have pet hair that you'd like removed?
            </label>
            <div className="grid grid-cols-2 gap-4">
              <button
                type="button"
                onClick={() => updateVehicle(index, { petHair: 'yes', petHairLevel: null })}
                className={`p-4 rounded-lg border-2 text-blue-900 ${vehicle.petHair === 'yes' ? 'border-blue-600 bg-blue-50 font-semibold' : 'border-gray-200 hover:border-blue-400'} transition-colors`}
              >
                Yes
              </button>
              <button
                type="button"
                onClick={() => updateVehicle(index, { petHair: 'no', petHairLevel: null })}
                className={`p-4 rounded-lg border-2 text-blue-900 ${vehicle.petHair === 'no' ? 'border-blue-600 bg-blue-50 font-semibold' : 'border-gray-200 hover:border-blue-400'} transition-colors`}
              >
                No
              </button>
            </div>
            {vehicle.petHair === 'yes' && (
              <div className="mt-4">
                <label className="block mb-2 text-blue-900">Select pet hair severity:</label>
                <select
                  value={vehicle.petHairLevel || ''}
                  onChange={(e) => updateVehicle(index, { petHairLevel: parseInt(e.target.value) as 1 | 2 | 3 })}
                  className="p-2 border rounded-lg w-full bg-white text-blue-900"
                >
                  <option value="">Choose level</option>
                  <option value="1">Level 1 (Light) - $75</option>
                  <option value="2">Level 2 (Moderate) - $100</option>
                </select>
                <p className="text-sm text-gray-600 mt-2">Heavy contamination may require additional fees</p>
              </div>
            )}
          </div>
          {vehicle.year && vehicle.make && vehicle.model && vehicle.petHair !== null ? (
            <>
              <div>
                <h4 className="text-md font-semibold mb-3 text-blue-900">Packages</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {vehicleConfig.car.packages
                    .filter(pkg => pkg.branches.includes(branch))
                    .map((pkg) => {
                      const sizeMultiplier = getSizeMultiplier();
                      const dynamicPrice = pkg.name === "Exterior" ? pkg.price : pkg.price * sizeMultiplier;
                      return (
                        <div key={pkg.name} className="relative">
                          {/* Selected Badge */}
                          {vehicle.package === pkg.name && (
                            <div className="selected-badge">
                              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                              </svg>
                              SELECTED
                            </div>
                          )}

                          <div
                            onClick={() => updateVehicle(index, { package: pkg.name })}
                            className={`border-2 rounded-lg cursor-pointer overflow-hidden ${vehicle.package === pkg.name
                              ? 'border-blue-600 bg-blue-50'
                              : 'border-gray-200 hover:border-blue-400'
                              }`}
                          >
                            <div className="relative">
                              <Slideshow images={pkg.images} />
                              <div className="absolute top-2 left-2 bg-white/80 backdrop-blur-lg p-2 rounded border border-white/30">
                                <div className="font-medium text-blue-900">{pkg.name}</div>
                              </div>
                              <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                ${dynamicPrice.toFixed(2)}
                              </div>
                            </div>
                            <div className="p-4">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setPopupContent({ title: pkg.name, images: pkg.images });
                                  setShowPopup(true);
                                }}
                                className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm inline-block hover:bg-blue-700 transition-colors"
                              >
                                See what's included
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className="mt-6">
                <h4 className="text-md font-semibold mb-3 text-blue-900">Add-Ons</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {vehicleConfig.car.addons
                    .filter(addon => addon.branches.includes(branch))
                    .map((addon) => (
                      <div key={addon.name} className="relative">
                        {vehicle.addons.some(a => a.name === addon.name) && (
                          <div className="selected-badge">
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                            SELECTED
                          </div>
                        )}
                        <div className="border border-gray-200 rounded-lg bg-white">
                          <div className="relative">
                            <Slideshow images={addon.images} />
                            <div className="absolute top-2 left-2 bg-white/80 backdrop-blur-lg p-2 rounded border border-white/30">
                              <div className="font-medium text-blue-900">{addon.name}</div>
                            </div>
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                              ${addon.price}
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="text-sm text-gray-600 mb-4">
                              {addon.description}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setPopupContent({ title: addon.name, images: addon.images });
                                  setShowPopup(true);
                                }}
                                className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm mt-2 inline-block hover:bg-blue-700 transition-colors"
                              >
                                See what's included
                              </button>
                            </div>
                            {addon.hasQuantity ? (
                              <div className="flex items-center gap-2">
                                <input
                                  type="number"
                                  min="0"
                                  value={vehicle.addons.find(a => a.name === addon.name)?.quantity || 0}
                                  onChange={(e) => {
                                    const quantity = Number(e.target.value);
                                    const updatedAddons = vehicle.addons.filter(a => a.name !== addon.name);
                                    if (quantity > 0) updatedAddons.push({ name: addon.name, quantity });
                                    updateVehicle(index, { addons: updatedAddons });
                                  }}
                                  className="p-2 border rounded w-20 text-center text-blue-900"
                                />
                                <span className="text-gray-600">quantity</span>
                              </div>
                            ) : (
                              <button
                                onClick={() => {
                                  const isAdding = !vehicle.addons.some(a => a.name === addon.name);
                                  const updatedAddons = isAdding
                                    ? [...vehicle.addons, { name: addon.name, quantity: 1 }]
                                    : vehicle.addons.filter(a => a.name !== addon.name);
                                  updateVehicle(index, { addons: updatedAddons });
                                  if (isAdding) {
                                    let warning = '';
                                    if (['Window Rock Chip Repair', 'Window Replacement'].includes(addon.name)) {
                                      warning = 'This service requires a specialized technician and will be scheduled separately.';
                                    } else if (['Oil Change, Washer Fluid & Tire Inflate'].includes(addon.name)) {
                                      warning = 'This service requires a specialized technician and may be scheduled separately. Oil and filter costs are in addition to the service price. Our technician will contact you to confirm your vehicle requirements before purchase.';
                                    }
                                    if (warning) {
                                      setPopupContent({ title: addon.name, images: addon.images, warning });
                                      setShowPopup(true);
                                    }
                                  }
                                }}
                                className={`w-full py-2 rounded-md relative overflow-hidden ${vehicle.addons.some(a => a.name === addon.name) ? 'bg-blue-600 text-white' : 'text-blue-600 hover:text-blue-700 border-2 border-blue-400 bg-transparent'}`}
                              >
                                {vehicle.addons.some(a => a.name === addon.name) ? 'Added' : 'Add'}
                                {!vehicle.addons.some(a => a.name === addon.name) && (
                                  <div className="absolute inset-0 -z-10 overflow-hidden">
                                    <div className="absolute -inset-[2px] bg-[linear-gradient(90deg,transparent_25%,theme(colors.blue.400)_50%,transparent_75%)] bg-[length:200%_auto] animate-border-shine rounded-md" />
                                  </div>
                                )}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}

                </div>
              </div>
            </>
          ) : (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg text-blue-900">
              Please complete all vehicle details above to view available packages and add-ons.
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          <div>
            <label className="block mb-2 text-blue-900">Vehicle Length (ft)</label>
            <input
              type="number"
              value={vehicle.length || ""}
              onChange={(e) => updateVehicle(index, { length: Number(e.target.value) })}
              className="p-2 border rounded-lg w-full bg-white text-blue-900"
              placeholder="Enter length in feet"
            />
            {vehicle.length && vehicle.length > 0 && (
              <div className="text-sm text-gray-600 mt-1">
                Calculating prices for {vehicle.length}ft
              </div>
            )}
          </div>

          <div>
            <h4 className="text-md font-semibold mb-3 text-blue-900">Services</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(vehicleConfig[vehicle.type] as VehicleConfig['boat'] | VehicleConfig['rv']).services
                .filter(service => service.branches.includes(branch))
                .map((service) => (
                  <div key={service.name} className="relative">
                    {vehicle.services.includes(service.name) && (
                      <div className="selected-badge">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        SELECTED
                      </div>
                    )}
                    <div
                      className={`border-2 rounded-lg cursor-pointer overflow-hidden ${vehicle.services.includes(service.name)
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-400'
                        }`}
                      onClick={() => {
                        const newServices = vehicle.services.includes(service.name)
                          ? vehicle.services.filter(s => s !== service.name)
                          : [...vehicle.services, service.name];
                        updateVehicle(index, { services: newServices });
                      }}
                    >
                      <div className="relative">
                        <Slideshow images={service.images} />
                        <div className="absolute top-0 left-0 right-0 flex justify-between items-center p-2">
                          <div className="bg-white/80 backdrop-blur-lg p-2 rounded border border-white/30 flex-1 mr-2">
                            <div className="font-medium text-blue-900 break-words">{service.name}</div>
                          </div>
                          <div className="bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg flex-shrink-0">
                            ${service.price}/ft
                          </div>
                        </div>
                      </div>
                      <div className="p-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setPopupContent({ title: service.name, images: service.images });
                            setShowPopup(true);
                          }}
                          className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm inline-block hover:bg-blue-700 transition-colors"
                        >
                          See what's included
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

            </div>
          </div>

          {config.addons.length > 0 && (
            <div className="mt-6">
              <h4 className="text-md font-semibold mb-3 text-blue-900">Add-Ons</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {config.addons
                  .filter(addon => addon.branches.includes(branch))
                  .map((addon) => (
                    <div key={addon.name} className="relative">
                      {vehicle.addons.some(a => a.name === addon.name) && (
                        <div className="selected-badge">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          SELECTED
                        </div>
                      )}
                      <div className="border border-gray-200 rounded-lg bg-white">
                        <div className="relative">
                          <Slideshow images={addon.images} />
                          <div className="absolute top-0 left-0 right-0 flex justify-between items-center p-2">
                            <div className="bg-white/80 backdrop-blur-lg p-2 rounded border border-white/30 flex-1 mr-2">
                              <div className="font-medium text-blue-900 break-words">{addon.name}</div>
                            </div>
                            <div className="bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg flex-shrink-0">
                              {addon.name === "roof" ? "$5/ft" : `$${addon.price}`}
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <div className="text-sm text-gray-600 mb-4">
                            {addon.description}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setPopupContent({ title: addon.name, images: addon.images });
                                setShowPopup(true);
                              }}
                              className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm mt-2 inline-block hover:bg-blue-700 transition-colors"
                            >
                              See what's included
                            </button>
                          </div>
                          {addon.hasQuantity ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="number"
                                min="0"
                                value={vehicle.addons.find(a => a.name === addon.name)?.quantity || 0}
                                onChange={(e) => {
                                  const quantity = Number(e.target.value);
                                  const updatedAddons = vehicle.addons.filter(a => a.name !== addon.name);
                                  if (quantity > 0) updatedAddons.push({ name: addon.name, quantity });
                                  updateVehicle(index, { addons: updatedAddons });
                                }}
                                className="p-2 border rounded w-20 text-center text-blue-900"
                              />
                              <span className="text-gray-600">quantity</span>
                            </div>
                          ) : (
                            <button
                              onClick={() => {
                                const isAdding = !vehicle.addons.some(a => a.name === addon.name);
                                let updatedServices = (vehicle.type === 'boat' || vehicle.type === 'rv')
                                  ? [...vehicle.services]
                                  : [];
                                let updatedAddons = isAdding
                                  ? [...vehicle.addons, { name: addon.name, quantity: 1 }]
                                  : vehicle.addons.filter(a => a.name !== addon.name);
                                if (addon.name === "roof" && vehicle.type === "rv" && isAdding) {
                                  const washService = "RV Exterior Wash, Light Algae Removal / Spray Wax & Towel Dry";
                                  if (!updatedServices.includes(washService)) {
                                    updatedServices.push(washService);
                                  }
                                }
                                updateVehicle(index, {
                                  addons: updatedAddons,
                                  ...(vehicle.type === "rv" && addon.name === "roof" ? { services: updatedServices } : {})
                                });
                              }}
                              className={`w-full py-2 rounded-md relative overflow-hidden ${vehicle.addons.some(a => a.name === addon.name) ? 'bg-blue-600 text-white' : 'text-blue-600 hover:text-blue-700 border-2 border-blue-400 bg-transparent'}`}
                            >
                              {vehicle.addons.some(a => a.name === addon.name) ? 'Added' : 'Add'}
                              {!vehicle.addons.some(a => a.name === addon.name) && (
                                <div className="absolute inset-0 -z-10 overflow-hidden">
                                  <div className="absolute -inset-[2px] bg-[linear-gradient(90deg,transparent_25%,theme(colors.blue.400)_50%,transparent_75%)] bg-[length:200%_auto] animate-border-shine rounded-md" />
                                </div>
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-6 pt-4 border-t border-blue-100">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-blue-900">Vehicle Total:</span>
          <span className="text-2xl font-bold text-blue-900">${vehicle.totalPrice.toFixed(2)}</span>
        </div>
      </div>

      {showPopup && (
        <ServicePopup
          title={popupContent.title}
          images={popupContent.images}
          onClose={() => setShowPopup(false)}
          warning={popupContent.warning}
        />
      )}
    </div>
  );
};


export default BookingSection;

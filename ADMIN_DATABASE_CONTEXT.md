# Database Structure Context for Admin Development

## Current Database Collections

### Core Collections:
- `branchConfigs` - Branch settings and contact info
- `vehiclePackages` - All services (cars, boats, RVs) 
- `vehicleAddons` - Car-specific add-ons only
- `vehicleSizes` - Car size multipliers
- `branchPricing` - Branch-specific pricing overrides

### Existing Collections (Don't Modify):
- `bookings` - Customer appointments and payment data
- `paymentLinks` - Stripe payment processing
- `users` - User authentication

## Vehicle Type Structure

### Single Collection Approach:
All vehicle types stored in `vehiclePackages` with `vehicleType` field:
- Cars: 3 packages (Interior, Exterior, Interior & Exterior)
- Boats: 4 services (Wash, Polish, Ceramic, etc.)
- RVs: 3 services (Wash, Roof Clean, Polish)

### Branch Availability:
- Cars: All 4 branches (lwr, w-stl, dvr, ny)
- Boats: Only 2 branches (lwr, w-stl) 
- RVs: All 4 branches

## Pricing Calculation Logic

### Car Pricing Formula:
```
Base Package Price × Size Multiplier + Add-ons = Total
Example: $249 × 1.1 (Small SUV) + $25 (Window Ceramic) = $299
```

### Size Multipliers:
- Sedan: 1.0x
- Small SUV: 1.1x  
- Large SUV: 1.15x
- Small Truck: 1.05x
- Large Truck: 1.1x
- Van: 1.7x

### Boat/RV Pricing:
Fixed service prices, no size multipliers or add-ons currently.

## Database Service Functions Available

```typescript
// Get all packages for a vehicle type and branch
getBranchPackages(branchId: string, vehicleType: 'car'|'boat'|'rv')

// Get addons (cars only)
getBranchAddons(branchId: string, vehicleType: string)

// Get size multipliers (cars only)  
getBranchVehicleSizes(branchId: string)

// Get branch configuration
getBranchConfig(branchId: string)
```

## Appointments/Bookings Structure

### Current Booking Flow:
1. Customer creates booking via BookingSection.tsx
2. Data stored in `bookings` collection with pricing snapshot
3. Payment handled via `paymentLinks` collection
4. Calendar events created from booking data

### Booking Data Includes:
- Vehicle details and selected services
- Calculated pricing (stored as snapshot)
- Customer info and address
- Selected date/time
- Branch assignment

## Branch-Specific Pricing Override

### Global vs Branch Pricing:
- Default: Use global prices from `vehiclePackages`
- Override: Store branch-specific prices in `branchPricing/{branchId}`
- Current: Interior Detailing = $249 (database) vs $248 (fallback)

## Admin Interface Needs

### Priority 1: Pricing Management
- Edit car package prices (especially Interior Detailing $249)
- Manage add-on pricing and availability
- Set branch-specific overrides

### Priority 2: Service Management  
- Enable/disable services per branch
- Manage boat/RV service availability
- Upload service images

### Priority 3: Branch Configuration
- Branch contact info and settings
- Service area management
- Operating hours

## Data Flow Summary

```
Customer Booking → Database Pricing → Calculation → Booking Record → Calendar Event
                ↑                     ↑
        Admin Changes Pricing    Applies Multipliers/Addons
```

## Important Notes

- **Cache Management**: Use `clearBranchCache()` after pricing changes
- **Fallback System**: Database-first with static fallback if connection fails  
- **Vehicle Types**: Handled in single collection with type filtering
- **Current Status**: Customer-side fully integrated, admin interface needed

This structure supports the multi-vehicle booking system with branch-specific pricing and service availability.

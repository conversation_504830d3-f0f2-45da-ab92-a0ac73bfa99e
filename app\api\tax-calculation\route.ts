// app/api/tax-calculation/route.ts
import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(req: NextRequest) {
    console.log('🔥 TaxAPI: POST request received', { timestamp: new Date().toISOString() });

    // Get the request body
    const { amount, address } = await req.json();

    console.log('🔥 TaxAPI: Request body parsed', { amount, address });

    // Initialize Stripe with your secret key - add type assertion
    const stripeSecretKey = process.env.STRIPE_LIVE_TAX_KEY;

    if (!stripeSecretKey) {
        console.error('🔥 TaxAPI: Stripe API key not configured');
        return NextResponse.json(
            { success: false, error: 'Stripe API key is not configured' },
            { status: 500 }
        );
    }

    console.log('🔥 TaxAPI: Stripe initialized, calling tax calculation API');
    const stripe = new Stripe(stripeSecretKey, {
    });

    try {
        // Call Stripe's tax calculation API
        const calculation = await stripe.tax.calculations.create({
            currency: 'usd',
            customer_details: {
                address: address,
                address_source: 'shipping',
            },
            line_items: [
                {
                    amount: amount,
                    reference: 'service_charge',
                    tax_behavior: 'exclusive',
                },
            ],
        });

        console.log('🔥 TaxAPI: Stripe calculation completed', {
            calculationId: calculation.id,
            taxAmount: calculation.tax_amount_exclusive,
            hasBreakdown: !!calculation.tax_breakdown?.length
        });

        // Get the actual tax rate from Stripe's breakdown
        const taxBreakdown = calculation.tax_breakdown?.[0];
        console.log('🔥 TaxAPI: Full tax breakdown:', JSON.stringify(taxBreakdown, null, 2));

        const actualTaxRate = taxBreakdown ? (taxBreakdown.tax_rate_details?.percentage_decimal || 0) : 0;

        // Convert to number and then to percentage if needed
        const numericTaxRate = typeof actualTaxRate === 'string' ? parseFloat(actualTaxRate) : actualTaxRate;
        const taxPercentage = numericTaxRate > 1 ? numericTaxRate : numericTaxRate * 100;

        console.log('🔥 TaxAPI: Tax calculation debug:', {
            taxAmount: calculation.tax_amount_exclusive,
            rawTaxRate: actualTaxRate,
            convertedTaxRate: taxPercentage,
            taxBreakdown: calculation.tax_breakdown
        });

        const response = {
            success: true,
            taxAmount: calculation.tax_amount_exclusive,
            totalAmount: amount + calculation.tax_amount_exclusive,
            taxPercentage: taxPercentage,
        };

        console.log('🔥 TaxAPI: Returning response:', response);

        // Return the calculation results
        return NextResponse.json(response);
    } catch (error: unknown) {
        console.error('🔥 TaxAPI: Tax calculation failed with error:', error);

        // Handle the error safely with type checking
        let errorMessage = 'Unknown error occurred';
        if (error instanceof Error) {
            errorMessage = error.message;
            console.error('🔥 TaxAPI: Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
        } else if (typeof error === 'string') {
            errorMessage = error;
        }

        const errorResponse = {
            success: false,
            error: errorMessage
        };

        console.log('🔥 TaxAPI: Returning error response:', errorResponse);

        return NextResponse.json(errorResponse, { status: 500 });
    }
}

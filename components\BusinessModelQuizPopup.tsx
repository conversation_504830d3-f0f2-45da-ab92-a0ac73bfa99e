"use client";
import React, { useState, useEffect } from "react";
import ShinyText from "@/components/ShinyText";

interface BusinessModelQuizPopupProps {
    onClose: () => void;
}

interface QuizQuestion {
    id: number;
    question: string;
    description: string;
    franchiseLabel: string;
    independentLabel: string;
}

interface FollowUpQuestion {
    id: string;
    question: string;
    description: string;
    placeholder: string;
}

interface FranchiseBenefits {
    reducedRisk: string;
    fasterBreakeven: string;
    groupPurchasing: string;
    brandAwareness: string;
    systemEfficiency: string;
    exitValue: string;
}

const BusinessModelQuizPopup: React.FC<BusinessModelQuizPopupProps> = ({ onClose }) => {
    // State management
    const [currentStep, setCurrentStep] = useState(1);
    const [contactName, setContactName] = useState("");
    const [contactEmail, setContactEmail] = useState("");
    const [contactPhone, setContactPhone] = useState("");
    const [foundUs, setFoundUs] = useState("");
    const [isEmailValid, setIsEmailValid] = useState(false);
    const [answers, setAnswers] = useState<Record<number, number>>({});
    const [followUpAnswers, setFollowUpAnswers] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);

    // Enhanced Quiz questions
    const questions: QuizQuestion[] = [
        {
            id: 1,
            question: "Business Experience",
            description: "My business experience is limited, and I prefer having guidance rather than learning everything through trial and error.",
            franchiseLabel: "Agree – I value structured guidance",
            independentLabel: "Disagree – I'm confident in self-teaching"
        },
        {
            id: 2,
            question: "Market Validation",
            description: "I'd rather join a concept already proven successful than test an unproven idea.",
            franchiseLabel: "Agree – Proven success matters",
            independentLabel: "Disagree – I trust my unique concept"
        },
        {
            id: 3,
            question: "Financial Predictability",
            description: "Access to projected financial models gives me more confidence than my own estimates.",
            franchiseLabel: "Agree – Benchmarks are crucial",
            independentLabel: "Disagree – I'm fine with uncertainty"
        },
        {
            id: 4,
            question: "Operational Support",
            description: "24/7 troubleshooting assistance would substantially reduce my stress.",
            franchiseLabel: "Agree – I need ongoing support",
            independentLabel: "Disagree – I can solve problems myself"
        },
        {
            id: 5,
            question: "Brand Recognition",
            description: "An established name would speed up customer acquisition compared to a new brand.",
            franchiseLabel: "Agree – Brand is a big advantage",
            independentLabel: "Disagree – I can build local reputation"
        },
        {
            id: 6,
            question: "Decision Making",
            description: "I prefer having guidelines for major decisions rather than making every call independently.",
            franchiseLabel: "Agree – Guardrails are valuable",
            independentLabel: "Disagree – I want full authority"
        },
        {
            id: 7,
            question: "Risk Tolerance",
            description: "Known failure rates and common pitfalls are more comforting than blazing my own trail.",
            franchiseLabel: "Agree – I prefer known risks",
            independentLabel: "Disagree – I embrace undefined risks"
        },
        {
            id: 8,
            question: "Startup Timeline",
            description: "A predictable launch timeline with clear milestones is more important than flexibility.",
            franchiseLabel: "Agree – I value structured timelines",
            independentLabel: "Disagree – I prefer flexibility"
        }
    ];


    // Enhanced Follow-up questions
    const followUpQuestions: FollowUpQuestion[] = [
        {
            id: "support",
            question: "How important is ongoing headquarters support to your business vision?",
            description: "Detail On The Go offers varying levels of ongoing support - from minimal guidance to comprehensive assistance with operations, marketing, and growth.",
            placeholder: "Share what kind of support would be most valuable..."
        },
        {
            id: "timeline",
            question: "What's your target timeline from signing to opening day?",
            description: "Detail On The Go provides structured launch timelines with proven milestones, often reducing opening time by 30-50% compared to independent businesses.",
            placeholder: "Describe your ideal business launch timeline..."
        },
        {
            id: "growth",
            question: "Do you envision eventually owning multiple locations?",
            description: "Detail On The Go provides clearer multi-unit expansion paths with territory rights and development incentives.",
            placeholder: "Share your long-term growth vision..."
        },
        {
            id: "work_life",
            question: "How do you envision your work-life balance as an owner?",
            description: "Detail On The Go typically offers more predictable owner workloads and clearer paths to hiring/management structures than independent businesses.",
            placeholder: "Describe your ideal involvement in day-to-day operations..."
        },
        {
            id: "challenges",
            question: "Which aspects of business ownership do you find most intimidating?",
            description: "Detail On The Go often eliminates the most challenging aspects of startup through proven systems, training, and support.",
            placeholder: "Share your biggest concerns about business ownership..."
        },
        {
            id: "industry",
            question: "How familiar are you with standard margins and metrics in this industry?",
            description: "Detail On The Go provides benchmarking data and performance metrics against similar units across the system.",
            placeholder: "Describe your industry knowledge level..."
        }
    ];

    // Franchise benefits for email results
    const franchiseBenefits: FranchiseBenefits = {
        reducedRisk: "DOTG's typically have a 15-20% higher success rate than independent businesses in the first 5 years",
        fasterBreakeven: "On average, DOTG's reach profitability 30% faster than independent startups in the same industry",
        groupPurchasing: "DOTG's systems often negotiate 20-40% lower supply costs through group purchasing power",
        brandAwareness: "Established brands can reduce customer acquisition costs by up to 60% compared to new businesses",
        systemEfficiency: "Established brands eliminate approximately 200+ operational decisions new independent owners must make",
        exitValue: "Established brands resale values typically command 15-25% higher multiples than comparable independent businesses"
    };

    // Calculate total steps
    const totalSteps = 1 + questions.length + followUpQuestions.length + 1;
    const contactStep = 1;
    const mainQuestionStart = 2;
    const mainQuestionEnd = mainQuestionStart + questions.length - 1;
    const followUpStart = mainQuestionEnd + 1;
    const followUpEnd = followUpStart + followUpQuestions.length - 1;
    const confirmationStep = totalSteps;

    // Email validation
    useEffect(() => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        setIsEmailValid(contactEmail ? re.test(contactEmail) : false);
    }, [contactEmail]);

    // Enhanced recommendation logic with more detailed franchise advice
    const getRecommendation = (f: number, ind: number) => {
        const diff = f - ind;
        const franchisePercent = Math.round((f / (f + ind)) * 100);

        if (franchisePercent > 70) {
            return {
                title: "Established Business Path Strongly Recommended",
                description: "Your responses show a strong alignment with an established business model. You value structure, support systems, and proven models. An established business and systems would likely provide the balance of guidance and opportunity you're seeking.",
                icon: "🚀"
            };
        } else if (franchisePercent > 55) {
            return {
                title: "Established Business-Leaning",
                description: "You show a preference for many established business benefits while still valuing some independence. Consider established business systems that provide strong support while allowing reasonable operational flexibility.",
                icon: "🚗"
            };
        } else if (franchisePercent > 45) {
            return {
                title: "Balanced Potential",
                description: "You value aspects of both franchising and independence equally. Consider either a flexible pre-made business system or an independent business with strong mentorship networks.",
                icon: "⚖️"
            };
        } else if (franchisePercent > 30) {
            return {
                title: "Independence-Leaning",
                description: "You favor independence but appreciate certain pre-figured business benefits. Consider starting independently while investing in support systems and mentorship to fill gaps.",
                icon: "🛠️"
            };
        } else {
            return {
                title: "Independent Path Strongly Recommended",
                description: "Your entrepreneurial profile shows a strong preference for autonomy and creativity. An independent business model would likely be more fulfilling for you.",
                icon: "🔨"
            };
        }
    };

    // Build enhanced HTML for the results email with more persuasive franchise information
    const formatResultsHtml = (fScore: number, iScore: number, recommendation: ReturnType<typeof getRecommendation>) => {
        const totalScore = fScore + iScore;
        const franchisePercent = Math.round((fScore / totalScore) * 100);
        const independentPercent = 100 - franchisePercent;

        const questionsHtml = questions
            .map(q => `<li><strong>${q.question}:</strong> ${answers[q.id] || 'N/A'}</li>`)
            .join('');
        const followUpHtml = followUpQuestions
            .map(fu => `<li><strong>${fu.question}:</strong> ${followUpAnswers[fu.id] || 'N/A'}</li>`)
            .join('');

        // pick benefits
        const selectedBenefits = [];
        if (franchisePercent > 40) selectedBenefits.push(franchiseBenefits.reducedRisk);
        if (franchisePercent > 50) selectedBenefits.push(franchiseBenefits.fasterBreakeven);
        if (franchisePercent > 60) selectedBenefits.push(franchiseBenefits.groupPurchasing);
        if (franchisePercent > 70) selectedBenefits.push(franchiseBenefits.brandAwareness);
        if (franchisePercent > 80) selectedBenefits.push(franchiseBenefits.systemEfficiency);

        const benefitsHtml = selectedBenefits.length
            ? `<h4>Key Benefits for You:</h4>
             <ul>${selectedBenefits.map(b => `<li>${b}</li>`).join('')}</ul>`
            : '';

        return `
          <p>Hi ${contactName},</p>
      
          <h2 style="margin:16px 0;">${recommendation.icon} ${recommendation.title}</h2>
          <p>${recommendation.description}</p>
      
          <div style="background:#f7f7f7;padding:12px;border-radius:4px;margin:20px 0;">
            <strong>Your Alignment:</strong>
            <p>${franchisePercent}% Franchise vs. ${independentPercent}% Independent</p>
          </div>
      
          ${benefitsHtml}
      
          <h4>Next Step</h4>
          <p>Reply with 2–3 times you’re free for a quick call, or reply back to this email.</p>
      
          <hr style="margin:30px 0;"/>
      
          <h4>Your Answers</h4>
          <ul>${questionsHtml}</ul>
      
          <h4>More Details</h4>
          <ul>${followUpHtml}</ul>
      
          <hr/>
      
          <p style="font-size:0.9em;color:#666;">
            Name: ${contactName} | Email: ${contactEmail} | Phone: ${contactPhone}
          </p>
        `;
    };

    // Send two emails: one to quiz taker, one to Levi
    // Send two emails: one to quiz taker, one to Levi
    const sendResultsEmails = async () => {
        setIsSubmitting(true);
        setSubmitError(null);

        // Attempt to add email to subscriber list
        try {
            const subscriberData = { email: contactEmail };
            const addSubscriberUrl = process.env.NEXT_PUBLIC_ADD_SUBSCRIBER;
            if (!addSubscriberUrl) {
                throw new Error("NEXT_PUBLIC_ADD_SUBSCRIBER is not defined");
            }
            const subscriberResponse = await fetch(addSubscriberUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(subscriberData),
            });
            if (!subscriberResponse.ok) {
                console.error("Failed to add subscriber:", subscriberResponse.statusText);
            }
        } catch (err) {
            console.error("Subscription error:", err);
        }

        // Proceed with sending results emails
        let fScore = 0;
        let iScore = 0;
        questions.forEach(q => {
            const val = answers[q.id] || 0;
            fScore += val;
            iScore += 5 - val;
        });

        const recommendation = getRecommendation(fScore, iScore);
        const htmlMessage = formatResultsHtml(fScore, iScore, recommendation);
        const payloads = [
            { to: contactEmail, subject: "Your Business Model Quiz Results from Detail on the Go" },
            { to: "<EMAIL>", subject: `New Quiz Submission: ${contactName}` }
        ];

        try {
            for (const p of payloads) {
                const payload = {
                    fromName: "Detail on the Go Quiz",
                    fromEmail: "<EMAIL>",
                    to: p.to,
                    subject: p.subject,
                    message: htmlMessage
                };
                const res = await fetch('/api/send-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                if (!res.ok) {
                    throw new Error(`Failed to send email to ${p.to}`);
                }
            }
            setCurrentStep(confirmationStep);
        } catch (err) {
            console.error("Submission Error:", err);
            setSubmitError(err instanceof Error ? err.message : "An unexpected error occurred.");
        } finally {
            setIsSubmitting(false);
        }
    };
    // Handlers
    const handleAnswerSelect = (questionId: number, value: number) => {
        if (!isSubmitting) {
            setAnswers(prev => ({ ...prev, [questionId]: value }));
        }
    };

    const handleFollowUpChange = (id: string, text: string) => {
        if (!isSubmitting) {
            setFollowUpAnswers(prev => ({ ...prev, [id]: text }));
        }
    };

    const handleNextStep = () => {
        if (currentStep < totalSteps - 1) {
            setCurrentStep(prev => prev + 1);
        } else if (currentStep === totalSteps - 1) {
            sendResultsEmails();
        } else {
            onClose();
        }
    };

    // Step logic
    const isContactStep = currentStep === contactStep;
    const isQuestionStep = currentStep >= mainQuestionStart && currentStep <= mainQuestionEnd;
    const isFollowUpStep = currentStep >= followUpStart && currentStep <= followUpEnd;
    const isConfirmationStep = currentStep === confirmationStep;

    const disableContinue = isSubmitting || (isContactStep
        ? !(contactName.trim() && isEmailValid && contactPhone.trim() && foundUs.trim())
        : isQuestionStep
            ? !answers[questions[currentStep - mainQuestionStart].id]
            : isFollowUpStep
                ? !followUpAnswers[followUpQuestions[currentStep - followUpStart].id]
                : false);

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl overflow-hidden border border-white/20">
                {/* Header + Progress */}
                <div className="bg-gradient-to-r from-blue-600 to-palatinate_blue p-6">
                    <div className="flex justify-between items-center mb-4">
                        <ShinyText text="Find Your Perfect Business Path" speed={5} className="text-3xl font-bold text-white" />
                        <button onClick={onClose} className="text-white hover:text-pink-300 transition-colors text-xl" disabled={isSubmitting}>✕</button>
                    </div>
                    <div className="w-full bg-white/30 rounded-full h-2 mb-2">
                        <div
                            className="bg-pink-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(currentStep / (totalSteps - 1)) * 100}%` }}
                        />
                    </div>
                    <div className="text-right text-white/80 text-sm">
                        Step {currentStep} of {totalSteps - 1}
                    </div>
                </div>

                {/* Body */}
                <div className="p-6 space-y-6">
                    {/* Contact Info Step */}
                    {isContactStep && (
                        <div className="space-y-6">
                            <h3 className="text-2xl font-semibold text-gray-900">Let's start with your contact info</h3>
                            <input
                                type="text"
                                placeholder="Your Name"
                                className="w-full p-4 border rounded-lg text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-blue-500 border-gray-300"
                                value={contactName}
                                onChange={e => setContactName(e.target.value)}
                                disabled={isSubmitting}
                            />
                            <input
                                type="tel"
                                placeholder="Your Phone"
                                className="w-full p-4 border rounded-lg text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-blue-500 border-gray-300"
                                value={contactPhone}
                                onChange={e => setContactPhone(e.target.value)}
                                disabled={isSubmitting}
                            />
                            <input
                                type="email"
                                placeholder="Your Email"
                                className={`w-full p-4 border rounded-lg text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-blue-500 ${isEmailValid || contactEmail === '' ? 'border-gray-300' : 'border-red-500'}`}
                                value={contactEmail}
                                onChange={e => setContactEmail(e.target.value)}
                                disabled={isSubmitting}
                            />
                            <input
                                type="text"
                                placeholder="How did you find us? e.g. Google, Friend"
                                className="w-full p-4 border rounded-lg text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-blue-500 border-gray-300"
                                value={foundUs}
                                onChange={e => setFoundUs(e.target.value)}
                                disabled={isSubmitting}
                                required
                            />
                            {!isEmailValid && contactEmail !== '' && (
                                <p className="text-red-500 text-sm mt-1">Please enter a valid email address.</p>
                            )}
                        </div>
                    )}

                    {/* Main Questions */}
                    {isQuestionStep && (() => {
                        const index = currentStep - mainQuestionStart;
                        const q = questions[index];
                        return (
                            <div className="space-y-6">
                                <h3 className="text-2xl font-semibold text-gray-900">{q.question}</h3>
                                <p className="text-gray-900 italic">"{q.description}"</p>
                                <div className="grid grid-cols-5 gap-2 mt-4">
                                    {Array.from({ length: 5 }, (_, i) => i + 1).map(val => (
                                        <button
                                            key={val}
                                            type="button"
                                            onClick={() => handleAnswerSelect(q.id, val)}
                                            className={`p-4 rounded-lg transition-all ${answers[q.id] === val ? 'bg-blue-600 text-white' : 'bg-white border border-gray-300 text-gray-900'} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-50'}`}
                                            disabled={isSubmitting}
                                        >
                                            {val}
                                        </button>
                                    ))}
                                </div>
                                <div className="flex justify-between text-sm text-gray-900">
                                    <span>{q.independentLabel}</span>
                                    <span>{q.franchiseLabel}</span>
                                </div>
                            </div>
                        );
                    })()}

                    {/* Follow-Up Questions */}
                    {isFollowUpStep && (() => {
                        const index = currentStep - followUpStart;
                        const fu = followUpQuestions[index];
                        return (
                            <div className="space-y-6">
                                <h3 className="text-2xl font-semibold text-gray-900">{fu.question}</h3>
                                <p className="text-gray-900">{fu.description}</p>
                                <textarea
                                    placeholder={fu.placeholder}
                                    className="w-full p-4 border rounded-lg h-40 text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-blue-500 border-gray-300"
                                    value={followUpAnswers[fu.id] || ''}
                                    onChange={e => handleFollowUpChange(fu.id, e.target.value)}
                                    disabled={isSubmitting}
                                />
                            </div>
                        );
                    })()}

                    {/* Confirmation Step */}
                    {isConfirmationStep && (
                        <div className="space-y-8 text-center">
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">Thank You!</h3>
                            <p className="text-gray-900">Your results have been sent to your email. Please check your inbox.</p>
                            <p className="text-gray-700 text-sm mt-4">We'll be in touch soon to discuss your personalized business path!</p>
                            <button
                                onClick={onClose}
                                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    )}

                    {/* Navigation */}
                    <div className="flex justify-between mt-8">
                        {currentStep > 1 && currentStep < confirmationStep && (
                            <button onClick={() => setCurrentStep(prev => prev - 1)} className={`px-6 py-2 text-blue-600 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:text-blue-700'} font-medium`} disabled={isSubmitting}>
                                ← Back
                            </button>
                        )}
                        {currentStep < confirmationStep && (
                            <button
                                onClick={handleNextStep}
                                disabled={disableContinue}
                                className={`ml-auto px-6 py-3 font-bold rounded-lg transition-all ${disableContinue ? 'bg-gray-300 text-gray-700 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/30'}`}
                            >
                                {isSubmitting && currentStep === totalSteps - 1 ? "Submitting..." : currentStep === totalSteps - 1 ? "Submit Quiz" : "Continue →"}
                            </button>
                        )}
                    </div>
                    {currentStep === totalSteps - 1 && submitError && (
                        <div className="mt-4 text-red-500">{submitError}</div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BusinessModelQuizPopup;
"use client";

import { helvetica } from "../fonts";
import cx from "classnames";
import Link from "next/link";
import { useState } from "react";
import BookingSection from "@/components/BookingSection";

export default function FAQ() {
    const [showBookingModal, setShowBookingModal] = useState(false);
    return (
        <div
            className={cx(
                helvetica.variable,
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-gray-100 font-sans"
            )}
        >
            {/* Booking Modal */}
            {showBookingModal && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        <div className="p-4 flex justify-between items-center border-b">
                            <h2 className="text-xl font-black tracking-tighter">Booking Details</h2>
                            <button
                                onClick={() => setShowBookingModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        <BookingSection onClose={() => setShowBookingModal(false)} />
                    </div>
                </div>
            )}
            {/* Header */}
            <div className="text-center py-10 px-6">
                <h1 className="text-4xl md:text-6xl font-bold text-white tracking-tighter">
                    F.A.Q.
                </h1>
                <h3 className="mt-6 text-lg text-white/90 md:text-xl font-medium">
                    Frequently Asked Questions about Detail On The Go
                </h3>
            </div>

            {/* FAQ Sections */}
            <div className="w-full max-w-6xl mx-auto mt-10 space-y-6 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-6">
                {/* Service Areas */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What are our service areas? 🗺️
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            To check if we service your location and schedule your appointment,{" "}
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="text-pink-500 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                            >
                                click here to book now
                            </button>
                            .
                        </p>
                    </div>
                </details>

                {/* Availability */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        When are you available?
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            To check our availability and schedule your appointment,{" "}
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="text-pink-500 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                            >
                                click here to book now
                            </button>
                            .
                        </p>
                    </div>
                </details>

                {/* Detail Packages */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What are our detail packages? 📦
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 space-y-4 min-h-32">
                        <p>
                            We offer comprehensive detailing services including:
                        </p>
                        <p>
                            <strong>Interior & Exterior Detail:</strong> Complete thorough cleaning for both interior and exterior surfaces, available at various service intervals.
                        </p>
                        <p>
                            <strong>Wash Detail:</strong> Premium exterior-only package featuring foam bath and professional tire dressing.
                        </p>
                        <p>
                            <strong>Interior Detail:</strong> Specialized interior cleaning with deep sanitization and restoration.
                        </p>
                        <p>
                            <strong>Ceramic Coating:</strong> Advanced long-lasting exterior protection technology.
                        </p>
                        <p>
                            <strong>Paint Correction & Polishing:</strong> Professional paint surface restoration and enhancement.
                        </p>
                        <p>
                            For complete details about our services,{" "}
                            <Link href="/services" className="text-pink-500 hover:underline">
                                visit our services page
                            </Link>
                            .
                        </p>
                    </div>
                </details>

                {/* Smoke Removal */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Can you remove smoke odors? 🚬
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Yes, we offer professional smoke odor removal services, including shampooing and surface extraction treatments. While we significantly reduce smoke odors, we cannot guarantee complete elimination due to the persistent nature of smoke residue.
                        </p>
                    </div>
                </details>

                {/* Rain Policy */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What happens if it rains? 🌧️
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Several of our detail vehicles are equipped with weatherproof protective awnings. If weather-resistant equipment is unavailable, we'll gladly reschedule your appointment at your convenience.
                        </p>
                    </div>
                </details>

                {/* Winter Service */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you provide service during winter? ❄️
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Detail services may be postponed during extremely cold weather conditions for quality assurance. Please contact your local detailer for specific weather-related policies and rescheduling options.
                        </p>
                    </div>
                </details>

                {/* Rescheduling */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I need to reschedule my appointment? 📅🔁
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Please contact your detailer at least 48 hours in advance, and they'll work with you to find a more convenient appointment time.
                        </p>
                    </div>
                </details>

                {/* Satisfaction Guarantee */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I'm not satisfied with my detail? 😥
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Customer satisfaction is our priority. Please contact us <NAME_EMAIL>, and we'll work diligently to resolve any concerns and ensure your complete satisfaction.
                        </p>
                    </div>
                </details>

                {/* Payment */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        How do I pay for my detail service? 💳💵
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            We accept both card and cash payments. After booking your appointment, you'll receive an email with a secure payment link for convenient online payment processing.
                        </p>
                    </div>
                </details>

                {/* Cancellations */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        What if I need to cancel my appointment? 🚫
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Please cancel at least 48 hours before your scheduled appointment. Contact your local detailer directly or email <NAME_EMAIL>.
                        </p>
                    </div>
                </details>

                {/* Vehicle Size Pricing */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Does vehicle size affect pricing? 🚙🏎️
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Yes, pricing varies based on vehicle size and type. For exact pricing specific to your vehicle,{" "}
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="text-pink-500 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                            >
                                click here to get a quote
                            </button>
                            .
                        </p>
                    </div>
                </details>

                {/* Service Frequency */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Can I adjust my detail service frequency? ⭕✔️⭕
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Absolutely! We love accommodating our clients' schedules and making it convenient for them to maintain a clean vehicle. Simply let your detailer know what works best for your schedule, and we'll do our best to accommodate your preferences.
                        </p>
                    </div>
                </details>

                {/* Pet Hair */}
                <details className="group border-b border-gray-700 py-4 min-h-20">
                    <summary className="flex justify-between items-center cursor-pointer text-lg font-semibold text-white tracking-tighter">
                        Do you handle pet hair removal? 🐕
                        <span className="text-pink-500 group-open:hidden">+</span>
                        <span className="text-pink-500 hidden group-open:inline">−</span>
                    </summary>
                    <div className="mt-4 text-gray-300 min-h-32">
                        <p>
                            Yes, we provide professional pet hair removal services. This service incurs an additional charge. For specific pricing for your vehicle,{" "}
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="text-pink-500 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
                            >
                                click here to get a quote
                            </button>
                            .
                        </p>
                    </div>
                </details>

                {/* CTA Section */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl text-center min-h-32">
                    <h2 className="text-2xl font-black text-white tracking-tighter">Need More Information?</h2>
                    <p className="mt-4 text-gray-300">We're here to help with all your detailing needs</p>
                    <Link
                        href="/contact"
                        className="mt-6 inline-block bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors tracking-tighter"
                    >
                        Contact Support
                    </Link>
                </div>
            </div>
        </div>
    );
}
import React, { useEffect, useRef, useState } from 'react';
import { locationValues } from './locationValues';

interface Address {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    lat?: number;
    lng?: number;
}

interface Branch {
    branch: string;
    businessNumber: string;
    calendarId: string;
    collectionId: string;
    location: string;
    link: string;
    introMp3: string;
    allowedEmails: string[];
    employeeName: string;
    employeeNumber: string;
    reviewLink: string;
    lat: number;
    lng: number;
    distance?: number;
}

interface AddressAutoCompleteProps {
    value?: Address;
    onAddressSelected?: (address: Address) => void;
    onBranchFound?: (branch: Branch) => void;
    onInputChange?: () => void;
}

const AddressAutoComplete: React.FC<AddressAutoCompleteProps> = ({ value, onAddressSelected, onBranchFound, onInputChange }) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [address, setAddress] = useState<Address>(
        value ? { ...value } : {
            street: '',
            city: '',
            state: '',
            postalCode: '',
            country: ''
        }
    );
    const [closestBranch, setClosestBranch] = useState<Branch | null>(null);
    useEffect(() => {
        const API_KEY = 'AIzaSyD4gsHevyhIxApmvBF0PQYUM4ZytptVX-E';
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${API_KEY}&libraries=places`;
        script.async = true;
        script.defer = true;
        script.onload = initAutocomplete;
        document.head.appendChild(script);

        return () => {
            if (document.head.contains(script)) {
                document.head.removeChild(script);
            }
        };
    }, []);

    useEffect(() => {
        if (inputRef.current && value) {
            const formatAddress = (addr: Address) => {
                const parts = [addr.street, addr.city, addr.state, addr.postalCode, addr.country].filter(Boolean);
                return parts.join(', ');
            };
            inputRef.current.value = formatAddress(value);
        }
    }, [value]);

    const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
        const R = 6371;
        const dLat = deg2rad(lat2 - lat1);
        const dLon = deg2rad(lon2 - lon1);
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    };

    const deg2rad = (deg: number): number => deg * (Math.PI / 180);

    const findClosestBranch = (lat: number, lng: number): Branch => {
        const branches = Object.values(locationValues);
        let closestBranch = branches[0];
        let minDistance = calculateDistance(lat, lng, branches[0].lat, branches[0].lng);

        for (let i = 1; i < branches.length; i++) {
            const branch = branches[i];
            const distance = calculateDistance(lat, lng, branch.lat, branch.lng);
            if (distance < minDistance) {
                minDistance = distance;
                closestBranch = branch;
            }
        }

        return { ...closestBranch, distance: minDistance };
    };

    const initAutocomplete = () => {
        if (!inputRef.current || !window.google || !window.google.maps || !window.google.maps.places) {
            console.warn('Google Maps API not ready, retrying in 500ms...');
            setTimeout(initAutocomplete, 500);
            return;
        }

        try {
            const autocomplete = new google.maps.places.Autocomplete(inputRef.current, {
                fields: ['address_components', 'formatted_address', 'geometry'],
                types: ['address']
            });

            autocomplete.addListener('place_changed', () => {
                try {
                    const place = autocomplete.getPlace();
                    if (!place.address_components || !place.geometry?.location) return;

                    const addressComponents = place.address_components;
                    const newAddress: Address = {
                        street: '',
                        city: '',
                        state: '',
                        postalCode: '',
                        country: '',
                        lat: place.geometry.location.lat(),
                        lng: place.geometry.location.lng()
                    };

                    for (const component of addressComponents) {
                        const componentType = component.types[0];
                        switch (componentType) {
                            case 'street_number':
                                newAddress.street = `${component.long_name} ${newAddress.street}`;
                                break;
                            case 'route':
                                newAddress.street += component.long_name;
                                break;
                            case 'locality':
                                newAddress.city = component.long_name;
                                break;
                            case 'administrative_area_level_1':
                                newAddress.state = component.short_name;
                                break;
                            case 'postal_code':
                                newAddress.postalCode = component.long_name;
                                break;
                            case 'country':
                                newAddress.country = component.long_name;
                                break;
                        }
                    }

                    setAddress(newAddress);

                    if (newAddress.lat && newAddress.lng) {
                        const branch = findClosestBranch(newAddress.lat, newAddress.lng);
                        setClosestBranch(branch);

                        if (onBranchFound) {
                            onBranchFound(branch);
                        }
                    }

                    if (onAddressSelected) {
                        onAddressSelected(newAddress);
                    }
                } catch (error) {
                    console.error('Error in place_changed listener:', error);
                }
            });
        } catch (error) {
            console.error('Error initializing Google Maps Autocomplete:', error);
        }
    };

    return (
        <div className="w-full">
            <div className="mb-2">
                
                <input
                    ref={inputRef}
                    id="address"
                    type="text"
                    placeholder="Enter your address"
                    onChange={(e) => {
                        // Call the onInputChange callback when user is typing
                        if (onInputChange) {
                            onInputChange();
                        }
                    }}
                    className="mt-1 w-full px-3 py-2 border-2 border-gray-600 rounded-md shadow-sm bg-white text-black focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
            </div>

        </div>
    );
};

declare global {
    interface Window {
        google: any;
    }
}

export default AddressAutoComplete;

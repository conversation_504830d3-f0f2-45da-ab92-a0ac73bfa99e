"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";
import FranchiseApplication from "@/components/FranchiseApplication";
import BusinessModelQuizPopup from "@/components/BusinessModelQuizPopup";
import { useEffect } from "react";
import OwnerEmailSignup from "@/components/Owner-Email-Signup";

const FranchisePage = () => {
    const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);
    // Add state to control popup visibility
    const [showApplication, setShowApplication] = useState(false);
    const [showQuiz, setShowQuiz] = useState(false);
    const [showEmailPopup, setShowEmailPopup] = useState(false);
    useEffect(() => {
        const timer = setTimeout(() => {
            setShowEmailPopup(true);
        }, 3000);

        return () => clearTimeout(timer);
    }, []);


    const faqItems = [
        {
            question: "How much does it cost to own a Detail On The Go franchise?",
            answer: "Detail On The Go offers multiple franchise plans to match your ambitions, with initial investments that include flexible pricing options, down payment plans, and our essential detailing starter kit. We recommend having at least $1,000 in reserve for unexpected expenses.* *Starter kits and investment amounts vary by market.",
        },
        {
            question: "How much revenue can I expect each month?",
            answer: "You're in the driver's seat! Your earnings potential depends on how many vehicles you choose to service. Most franchisees start seeing $2,500-$4,000/month in their first six months working part-time.",
        },
        {
            question: "Do I need detailing experience?",
            answer: "Not required. Our online training program includes training covering paint correction, ceramic coatings, interior detailing, and customer service. You'll graduate ready to deliver showroom-quality results.",
        },
        {
            question: "Do I need to find my own clients?",
            answer: "We've got your back. Our digital marketing team generates local leads through Google Ads, social media, and our booking platform.",
        },
        {
            question: "What equipment do I need?",
            answer: "Our Pro Starter Kit ($2,495 value) includes everything for mobile detailing: pressure washer, vacuum, polisher, and premium chemicals. Add-ons like water reclamation systems or van wraps are optional.",
        },
        {
            question: "Can I keep my current job?",
            answer: "Absolutely. Many owners start detailing evenings/weekends. Our scheduling software lets you manage appointments around your existing commitments. Dedicate 15+ hours/week to see meaningful income.",
        },
        {
            question: "Do you require background checks?",
            answer: "Yes. All franchise applicants undergo comprehensive background screening. Our clients trust us with their luxury vehicles - we maintain the highest professional standards.",
        },

        {
            question: "How do I get started?",
            answer: "1. Complete our franchise questionnaire 2. Join a discovery call 3. Review our FDD 4. Launch your business. Most candidates complete the process in 1-3 weeks.",
        },
    ];



    const whatWeProvide = [
        "Professional branding",
        "A dedicated website & a local online business page",
        "Client communication tools",
        "All operational systems",
        "Select equipment, chemicals, supplies, and company apparel",
        "Online marketing and a social media team to promote your services",
        "Flexible part-time or full-time work options",
        "Comprehensive training to set you up for success",
    ];

    return (
        <main className="min-h-screen bg-transparent font-[Helvetica,Arial,sans-serif]">
            {/* Hero Section */}
            <section className="w-full py-12 md:pt-16 md:pb-8">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-4">
                        <ShinyText
                            text="Own the Cleanest Business on the Planet."
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold italic text-white tracking-tight"

                        />
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        If you have the drive and desire to make a better life for you and your family, let Detail On The Go take care of you while you take care of your business and lifelong dreams.                    </p>

                    <div className="flex flex-col items-center space-y-6 px-8">
                        <div className="relative">
                            <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                                <span className="inline-block wave-animation">👉</span>
                            </span>
                            <button
                                onClick={() => setShowApplication(true)}
                                className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-xl">
                                    REQUEST INFO
                                </span>
                                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </button>
                        </div>

                        <div className="text-center text-sm font-medium text-white/80 my-1">- OR -</div>

                        <div className="relative">
                            <button
                                onClick={() => setShowQuiz(true)}
                                className="group relative bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-blue-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-xl">
                                    FIND OUT: SHOULD I FRANCHISE OR DIY?
                                </span>
                                <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </button>
                            <p className="text-sm mt-2 text-white/80">Take our quick quiz to see if franchising is right for you</p>
                        </div>
                    </div>
                </div>
                {/* Added Image */}
                <div className="w-full mt-8 max-w-4xl mx-auto">
                    <Image
                        src="/help-wanted.png"
                        alt="Join Our Team"
                        width={1200}
                        height={600}
                        className="rounded-lg shadow-xl border-4 border-white"
                    />
                </div>
                {/* Placeholder for the application or quiz modals */}
                {showApplication && <div>Application Form Would Appear Here</div>}
                {showQuiz && <div>Quiz Would Appear Here</div>}
            </section>

            {/* Conditionally render franchise application popup */}
            {showApplication && (
                <FranchiseApplication onClose={() => setShowApplication(false)} />
            )}
            {showQuiz && (
                <BusinessModelQuizPopup onClose={() => setShowQuiz(false)} />
            )}
            {showEmailPopup && <OwnerEmailSignup onClose={() => setShowEmailPopup(false)} />}
            {/* Why Franchise Section - Changed to pink theme */}
            <section className="w-full py-16 bg-pink-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">
                        Why We Win — and Keep Winning
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {[
                            {
                                icon: "💰",
                                title: "Unlimited Earning Potential",
                                description: "Your income grows with every vehicle you service. Start small detailing 10-15 cars monthly, then scale to full-time earnings by adding technicians. Our top franchisees clear $10k+/month managing teams while working 20 hours weekly."
                            },
                            {
                                icon: "⏰",
                                title: "Work On Your Terms",
                                description: "Build your schedule around life - not the other way around. Take mornings off for school runs, detail evenings only, or go all-in full-time. Our mobile model means no shop overhead, and you control when/where you work."
                            },
                            {
                                icon: "🛠️",
                                title: "Full-Service Support System",
                                description: "We handle client acquisition through digital marketing while you focus on perfecting details. Get turnkey equipment packages, ongoing training, and our proven business playbook. No prior experience needed - just bring the hustle."
                            },
                        ].map((item, idx) => (
                            <div key={idx} className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                                <div className="text-5xl mb-4 text-white">{item.icon}</div>
                                <h3 className="text-xl font-semibold mb-2 text-white">{item.title}</h3>
                                <p className="text-gray-200">{item.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Two Paths Section - Changed to palatinate_blue theme to match your layout */}
            <section className="w-full py-16 bg-palatinate_blue/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">
                        Choose Your Franchise Path
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-palatinate_blue-800 p-8 rounded-2xl border-2 border-pink-400 hover:border-pink-300 transition-all">
                            <h3 className="text-2xl font-bold text-pink-400 mb-4 flex items-center gap-3">
                                <span>🚗</span> Hustler's Path ($300)
                            </h3>
                            <p className="text-gray-300 mb-6">Perfect for bootstrappers</p>
                            <ul className="space-y-3 text-gray-200">
                                <li>✅ Low $300 initial license investment</li>
                                <li>✅ Start with your own vehicle & equipment or use our pre-made kits</li>
                                <li>✅ Training & guidance provided</li>
                                <li>✅ Optional starter kits & branded van lease</li>
                                <li>✅ Full CRM & business systems included</li>
                                <li>✅ Marketing campaigns (Email, Google, Facebook, etc...)</li>
                                <li>✅ Google Business setup & management</li>
                            </ul>
                        </div>
                        <div className="bg-palatinate_blue-800 p-8 rounded-2xl border-2 border-blue-400 hover:border-blue-300 transition-all">
                            <h3 className="text-2xl font-bold text-blue-400 mb-4 flex items-center gap-3">
                                <span>🚐</span> Pro Path
                            </h3>
                            <p className="text-gray-300 mb-6">Maximum impact from day one</p>
                            <ul className="space-y-3 text-gray-200">
                                <li>✅ Larger initial investment</li>
                                <li>✅ Fully equipped and branded van</li>
                                <li>✅ Territory exclusivity guaranteed</li>
                                <li>✅ Rights to resell your location</li>
                                <li>✅ Option to develop & sub-franchise territories</li>
                                <li>✅ Premium marketing campaigns (Email, Google, Facebook)</li>
                                <li>✅ Enhanced Google Business & search optimization</li>
                                <li>✅ Complete systems & support package</li>
                                <li>✅ Priority access to new marketing initiatives</li>
                                <li>✅ All services included (Mobile Oil Changes, Paintless Dent Removal, etc...)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            {/* FAQ Section - Changed to a different color theme to match palette */}
            <section className="w-full py-16 bg-blue-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">
                        Frequently Asked Questions
                    </h2>
                    <div className="space-y-4">
                        {faqItems.map((item, index) => (
                            <div key={index} className="bg-blue-700 rounded-lg border border-blue-500">
                                <button
                                    onClick={() =>
                                        setOpenFaqIndex(openFaqIndex === index ? null : index)
                                    }
                                    className="w-full p-6 text-left flex justify-between items-center hover:bg-blue-600 transition-colors"
                                >
                                    <h3 className="text-xl font-semibold text-white">{item.question}</h3>
                                    <span className="text-2xl text-white ml-4">{openFaqIndex === index ? "−" : "+"}</span>
                                </button>
                                {openFaqIndex === index && (
                                    <div className="p-6 pt-0 border-t border-blue-500">
                                        <p className="text-gray-200 leading-relaxed">{item.answer}</p>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Enhanced "What We Provide" Intro */}
            <section className="w-full py-16 bg-gradient-to-br from-white/50 to-blue-100/50 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4 text-center">  {/* ← here */}

                    {/* 1) Centered, button-style headline */}
                    <h2
                        className="group relative inline-block bg-blue-700 text-white px-8 py-4 rounded-lg font-extrabold text-5xl md:text-6xl
             transition-all transform hover:scale-105 hover:-translate-y-1 hover:bg-blue-800 hover:shadow-lg hover:shadow-blue-700 shiny-button border border-white"
                    >
                        <span className="relative z-10">📦 Your Complete Business Package 📦</span>
                        <span
                            className="absolute inset-0 bg-gradient-to-r from-blue-700 to-blue-900 opacity-0
               group-hover:opacity-100 transition-opacity duration-300 rounded-lg"
                        />
                    </h2>

                    {/* 2) Punchy subhead */}
                    <p className="text-lg md:text-xl text-gray-700 mt-6 mb-12 tracking-wide">
                        Skip the <span className="font-semibold text-blue-600">expensive learning curve </span>
                        and hit the ground running with everything you need to succeed
                    </p>

                    {/* 3) "Investment call-out" card for years & dollars */}
                    <div
                        className="max-w-2xl mx-auto mb-12 bg-white/80 backdrop-blur-lg p-8 rounded-2xl 
             border-l-4 border-pink-500 shadow-lg flex items-start gap-4"
                    >
                        <div className="text-pink-500 text-5xl leading-none">⚡</div>
                        <div>
                            <h3 className="text-2xl font-bold mb-2 text-gray-900">
                                Built on <span className="text-blue-600">4½ Years</span> &amp;
                                <span className="text-green-600"> $200K+</span> Investment
                            </h3>
                            <p className="text-gray-800 leading-relaxed">
                                Our turnkey system saves you the equivalent of <em>years</em> of
                                trial-and-error and tens of thousands in outside fees.
                            </p>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Professional Branding */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $3,500+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🎨</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Professional Branding</h3>
                            <p className="text-gray-700">
                                Custom logo design, brand guidelines, and professional identity that would take 4-6 weeks and
                                thousands to develop on your own
                            </p>
                        </div>

                        {/* Website & Business Page */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $5,000+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">💻</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Dedicated Website & Local Page</h3>
                            <p className="text-gray-700">
                                Professional web presence that converts visitors to customers — saving you 2-3 months of development
                                time and thousands in design costs
                            </p>
                        </div>

                        {/* Client Communication Tools */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $1,800+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">📱</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Client Communication Tools</h3>
                            <p className="text-gray-700">
                                Automated booking, reminders, and follow-ups that would take months to set up and test properly
                            </p>
                        </div>

                        {/* Operational Systems */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $4,200+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">⚙️</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">All Operational Systems</h3>
                            <p className="text-gray-700">
                                Proven workflows, scheduling, and business management that would take 6+ months of trial and error to
                                develop yourself
                            </p>
                        </div>

                        {/* Equipment & Supplies */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $2,500+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🧰</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Equipment, Chemicals & Apparel</h3>
                            <p className="text-gray-700">
                                Start with premium supplies at wholesale rates, saving weeks of research and preventing costly mistakes
                                on ineffective products
                            </p>
                        </div>

                        {/* Professional Marketing */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-pink-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $8,000+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">📈</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Professional Marketing & Ad Campaigns</h3>
                            <p className="text-gray-700">
                                In-house experts manage your campaigns to prevent the $1,000s in wasted ad spend that plague new businesses.
                                Includes social media management and lead generation
                            </p>
                        </div>

                        {/* Flexible Work Options */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                PRICELESS
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">⏰</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Flexible Work Options</h3>
                            <p className="text-gray-700">
                                Build your business part-time or full-time with systems designed to accommodate your lifestyle and goals
                            </p>
                        </div>

                        {/* Comprehensive Training */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $7,500+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🎓</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Comprehensive Training</h3>
                            <p className="text-gray-700">
                                Skip years of expensive trial-and-error with our expert training program that covers every aspect of running
                                a successful detailing business
                            </p>
                        </div>

                        {/* Ongoing Support */}
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 hover:shadow-lg transition-all hover:translate-y-[-5px] group relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-pink-500 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                                $12,000+ VALUE
                            </div>
                            <div className="text-pink-600 text-3xl mb-3">🤝</div>
                            <h3 className="text-lg font-bold text-gray-800 mb-2">Ongoing Expert Support</h3>
                            <p className="text-gray-700">
                                Daily access to industry experts who've solved every challenge you'll face — like having a $100K/yr business
                                consultant on speed dial
                            </p>
                        </div>
                    </div>

                    {/* UPDATED: Summary card highlighting investment */}
                    <div className="mt-12 p-6 bg-gradient-to-r from-pink-500 to-blue-500 rounded-xl text-center text-white">
                        <h3 className="text-2xl font-bold mb-2">4.5 Years &amp; $200K+ Reinvested Internally</h3>
                        <p className="text-lg mb-2">
                            Equivalent DIY cost: <strong>$300K+</strong> — DIY timeline: <strong>6+ years</strong>
                        </p>
                        <p className="text-lg">
                            Get everything right out of the box for a fraction of the time and money.
                        </p>
                    </div>
                </div>
            </section>

            {/* ─── Vision & Clarity ───
     Replace your existing Franchisee Testimonials section with this */}
            <section className="w-full py-20 bg-gradient-to-br from-pink-500 to-blue-600 text-white">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <h2 className="text-4xl md:text-5xl font-extrabold mb-6">
                        See Your Future. Own Your Success.
                    </h2>
                    <p className="text-lg md:text-xl mb-10 max-w-3xl mx-auto leading-relaxed">
                        Imagine leading a team of skilled pros who trust your training, delivering five-star detailing every time.
                        In just 12–18 months, that vision becomes your reality:
                        <strong> consistent 6-figure revenues, a trained crew,</strong> and a business you can step back from—yet still watch thrive.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-white bg-opacity-20 p-8 rounded-2xl backdrop-blur-sm">
                            <h3 className="text-2xl font-semibold mb-2">Year 1: Foundation</h3>
                            <p className="mb-4">
                                Learn our turnkey system, book your first 100 jobs, and build an initial client base
                                that fuels referrals.
                            </p>
                            <p className="font-bold">Goal: $80K+ revenue & first hire</p>
                        </div>
                        <div className="bg-white bg-opacity-20 p-8 rounded-2xl backdrop-blur-sm">
                            <h3 className="text-2xl font-semibold mb-2">Year 2: Expansion</h3>
                            <p className="mb-4">
                                Scale to multiple teams using our hiring template, onboard advanced services,
                                and watch your margins grow.
                            </p>
                            <p className="font-bold">Goal: $150K+ revenue & 2–3 technicians</p>
                        </div>
                        <div className="bg-white bg-opacity-20 p-8 rounded-2xl backdrop-blur-sm">
                            <h3 className="text-2xl font-semibold mb-2">Year 3: Leadership</h3>
                            <p className="mb-4">
                                Step into a strategic role—focus on marketing, territory growth, and new service lines
                                while your team delivers excellence.
                            </p>
                            <p className="font-bold">Goal: $250K+ revenue & self-managed crews</p>
                        </div>
                        <div className="bg-white bg-opacity-20 p-8 rounded-2xl backdrop-blur-sm">
                            <h3 className="text-2xl font-semibold mb-2">Beyond: Legacy</h3>
                            <p className="mb-4">
                                Own a business built to sell: turnkey processes, a loyal customer base, and
                                recurring revenue that commands top valuation.
                            </p>
                            <p className="font-bold">Goal: High-margin exit or multi-unit empire</p>
                        </div>
                    </div>

                </div>
            </section>



            {/* Soft Comparison Section */}
            <section className="w-full py-16 bg-palatinate_blue-700/20 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h2 className="text-2xl font-bold text-white mb-6">
                        Should you open a franchise or DIY?
                    </h2>
                    <p className="text-white/90 max-w-2xl mx-auto mb-8">
                        While some consider building from scratch, our franchise owners typically reach profitability 3x faster with half the stress. Let's compare paths:
                    </p>
                    <button
                        onClick={() => setShowQuiz(true)}
                        className="group relative bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-blue-500 shiny-button border border-white"
                    >
                        <span className="relative z-10 italic font-bold text-xl">
                            FIND OUT: SHOULD I FRANCHISE OR DIY?
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                    </button>
                </div>
            </section>
            {/* Call to Action Section */}
            <section className="w-full max-w-3xl mx-auto bg-white/70 backdrop-blur-lg rounded-xl border border-white/30 shadow-md p-8 text-center mb-16">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">Ready to Start Your Journey?</h2>
                <p className="text-lg text-gray-700 mb-6">
                    Book a consultation with us today and take the first step towards owning your own mobile detailing business!
                </p>
                <div className="flex flex-col md:flex-row items-center justify-center gap-4">
                    <button
                        onClick={() => setShowApplication(true)}
                        className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                    >
                        <span className="relative z-10 italic font-bold text-xl">
                            REQUEST INFO
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                    </button>

                </div>
            </section>
            {/* Franchise Disclaimer Section */}
            <section className="w-full py-8 bg-palatinate_blue-800/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                        <h3 className="text-lg font-semibold mb-3 text-white/90">Franchise Disclosure</h3>
                        <p className="text-sm text-gray-300 leading-relaxed">
                            <span className="font-semibold text-white/80">Note:</span> None of the communications made through this webpage should be construed as an offer to sell any Detail On The Go franchises in, nor is any such communication directed to, the residents of any jurisdiction requiring registration of the franchise before it is offered or sold in that jurisdiction. No Detail On The Go franchises will be sold to any resident of such jurisdiction until the offering has been exempted from the requirements of, or duly registered in and declared effective by, such jurisdiction and any required Franchise Disclosure Document has been delivered to the prospective franchisee before the sale in compliance with applicable law. If you have any questions concerning the registration status of franchises in your jurisdiction, please contact our Franchise Development department.
                        </p>

                    </div>
                    <div className="mt-4 text-center">
                        <p className="text-xs text-gray-400">
                            © {new Date().getFullYear()} Detail On The Go Franchising, LLC. All rights reserved.
                        </p>
                    </div>
                </div>
            </section>
            <section className="w-full py-16 bg-gradient-to-br from-palatinate_blue/90 to-blue-700/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-white mb-4">
                            🗺️ Available Franchise Territories
                        </h2>
                        <p className="text-lg text-white/90 max-w-2xl mx-auto">
                            Secure your exclusive territory in these high-opportunity markets. Prime locations still available for motivated entrepreneurs.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {[
                            {
                                city: "Kansas City",
                                state: "MO/KS",
                                url: "/franchise/kansas-city",
                                icon: "🏙️",
                                highlight: "Metro Hub"
                            },
                            {
                                city: "Dallas-Fort Worth",
                                state: "TX",
                                url: "/franchise/dallas-fort-worth",
                                icon: "🌟",
                                highlight: "Mega Market"
                            },
                            {
                                city: "Houston",
                                state: "TX",
                                url: "/franchise/houston",
                                icon: "🚀",
                                highlight: "Energy Capital"
                            },
                            {
                                city: "Denver",
                                state: "CO",
                                url: "/franchise/denver",
                                icon: "⛰️",
                                highlight: "Mile High"
                            },
                            {
                                city: "Phoenix",
                                state: "AZ",
                                url: "/franchise/phoenix",
                                icon: "🌵",
                                highlight: "Desert Oasis"
                            }
                        ].map((location, idx) => (
                            <a
                                key={idx}
                                href={location.url}
                                className="group bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20 hover:bg-white/20 hover:border-pink-400 hover:shadow-lg hover:shadow-pink-500/20 transition-all duration-300 hover:translate-y-[-5px]"
                            >
                                <div className="flex items-start justify-between mb-3">
                                    <div className="text-3xl">{location.icon}</div>
                                    <span className="bg-pink-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                        {location.highlight}
                                    </span>
                                </div>
                                <h3 className="text-xl font-bold text-white group-hover:text-pink-300 transition-colors">
                                    {location.city}
                                </h3>
                                <p className="text-gray-300 text-sm mb-3">{location.state}</p>
                                <div className="flex items-center text-pink-400 font-semibold group-hover:text-pink-300">
                                    <span>Learn More</span>
                                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                                </div>
                            </a>
                        ))}

                        {/* Call-to-action card */}
                        <div className="bg-gradient-to-br from-pink-500 to-pink-600 p-6 rounded-xl border border-pink-400 shadow-lg hover:shadow-pink-500/30 transition-all duration-300 hover:translate-y-[-5px] flex flex-col justify-center items-center text-center">
                            <div className="text-4xl mb-3">🎯</div>
                            <h3 className="text-xl font-bold text-white mb-2">Don't See Your City?</h3>
                            <p className="text-pink-100 text-sm mb-4">We're expanding rapidly. Your market might be next!</p>
                            <button
                                onClick={() => setShowApplication(true)}
                                className="bg-white text-pink-600 px-4 py-2 rounded-lg font-semibold hover:bg-pink-50 transition-colors text-sm"
                            >
                                Inquire Now
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Professional "As Seen On" Section */}
            <section className="w-full py-16 bg-gradient-to-br from-white/80 to-gray-100/80 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-800 mb-4">
                            📺 As Featured In Leading Media
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Our proven franchise model has been recognized by top industry publications and media outlets nationwide.
                        </p>
                    </div>

                    <div className="bg-white/60 backdrop-blur-lg p-8 rounded-2xl border border-gray-200 shadow-lg">
                        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12">
                            {[
                                { src: "/references/reference23.png", alt: "Reference Logo 23" },
                                { src: "/references/reference24.png", alt: "Reference Logo 24" },
                                { src: "/references/reference25.png", alt: "Reference Logo 25" },
                                { src: "/references/reference26.png", alt: "Reference Logo 26" }
                            ].map((ref, idx) => (
                                <div
                                    key={idx}
                                    className="group p-4 bg-white/50 rounded-lg border border-gray-200/50 hover:bg-white/80 hover:shadow-md transition-all duration-300 hover:translate-y-[-2px]"
                                >
                                    <img
                                        src={ref.src}
                                        alt={ref.alt}
                                        className="h-16 w-auto object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                                        loading="lazy"
                                    />
                                </div>
                            ))}
                        </div>

                        <div className="mt-8 text-center">
                            <p className="text-sm text-gray-500 italic">
                                Trusted by industry leaders • Featured in national publications • Recognized for excellence
                            </p>
                        </div>
                    </div>

                    {/* Trust indicators */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                        <div className="text-center p-6 bg-white/40 backdrop-blur-lg rounded-xl border border-gray-200">
                            <div className="text-3xl mb-2">📈</div>
                            <h3 className="font-bold text-gray-800 mb-1">Industry Recognition</h3>
                            <p className="text-sm text-gray-600">Featured across major business publications</p>
                        </div>
                        <div className="text-center p-6 bg-white/40 backdrop-blur-lg rounded-xl border border-gray-200">
                            <div className="text-3xl mb-2">🏆</div>
                            <h3 className="font-bold text-gray-800 mb-1">Award-Winning Model</h3>
                            <p className="text-sm text-gray-600">Recognized for franchise innovation</p>
                        </div>
                        <div className="text-center p-6 bg-white/40 backdrop-blur-lg rounded-xl border border-gray-200">
                            <div className="text-3xl mb-2">⭐</div>
                            <h3 className="font-bold text-gray-800 mb-1">Media Coverage</h3>
                            <p className="text-sm text-gray-600">Featured stories and success highlights</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default FranchisePage;
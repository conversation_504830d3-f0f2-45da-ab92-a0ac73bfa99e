import "./ShinyText.css";

interface ShinyTextProps {
    text: string;           // Required string prop
    disabled?: boolean;     // Optional boolean prop
    speed?: number;         // Optional number prop
    className?: string;     // Optional string prop
}

const ShinyText = ({
    text,
    disabled = false,
    speed = 5,
    className = "",
}: ShinyTextProps) => {
    const animationDuration = `${speed}s`;

    return (
        <div
            className={`shiny-text ${disabled ? "disabled" : ""} ${className}`}
            style={{ animationDuration }}
        >
            {text}
        </div>
    );
};

export default ShinyText;
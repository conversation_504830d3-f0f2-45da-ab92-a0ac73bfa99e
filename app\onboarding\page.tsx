"use client";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";

const OnboardingPage = () => {
    const [formData, setFormData] = useState({
        // Personal Information
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        address: "",
        city: "",
        state: "",
        zipCode: "",
        
        // Identification
        driversLicenseNumber: "",
        driversLicenseState: "",
        ssn: "",
        dateOfBirth: "",
        
        // Emergency Contact
        emergencyContactName: "",
        emergencyContactRelationship: "",
        emergencyContactPhone: "",
        emergencyContactEmail: "",
        
        // Vehicle Information
        vehicleYear: "",
        vehicleMake: "",
        vehicleModel: "",
        vehicleColor: "",
        vehicleLicensePlate: "",
        vehicleInsurance: "",
        
        // Additional Information
        startDate: "",
        position: "",
        previousExperience: "",
        additionalNotes: "",
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitMessage("");

        // Format the email message for internal notification
        const internalMessage = `<h2>New Team Member Onboarding Submission</h2>
<hr>
<h3>Personal Information</h3>
<ul>
    <li><strong>Name:</strong> ${formData.firstName} ${formData.lastName}</li>
    <li><strong>Email:</strong> ${formData.email}</li>
    <li><strong>Phone:</strong> ${formData.phone}</li>
    <li><strong>Address:</strong> ${formData.address}, ${formData.city}, ${formData.state} ${formData.zipCode}</li>
    <li><strong>Date of Birth:</strong> ${formData.dateOfBirth}</li>
</ul>
<hr>
<h3>Identification</h3>
<ul>
    <li><strong>Driver's License:</strong> ${formData.driversLicenseNumber} (${formData.driversLicenseState})</li>
    <li><strong>SSN:</strong> ${formData.ssn}</li>
</ul>
<hr>
<h3>Emergency Contact</h3>
<ul>
    <li><strong>Name:</strong> ${formData.emergencyContactName}</li>
    <li><strong>Relationship:</strong> ${formData.emergencyContactRelationship}</li>
    <li><strong>Phone:</strong> ${formData.emergencyContactPhone}</li>
    <li><strong>Email:</strong> ${formData.emergencyContactEmail}</li>
</ul>
<hr>
<h3>Vehicle Information</h3>
<ul>
    <li><strong>Vehicle:</strong> ${formData.vehicleYear} ${formData.vehicleMake} ${formData.vehicleModel}</li>
    <li><strong>Color:</strong> ${formData.vehicleColor}</li>
    <li><strong>License Plate:</strong> ${formData.vehicleLicensePlate}</li>
    <li><strong>Insurance:</strong> ${formData.vehicleInsurance}</li>
</ul>
<hr>
<h3>Employment Information</h3>
<ul>
    <li><strong>Position:</strong> ${formData.position}</li>
    <li><strong>Start Date:</strong> ${formData.startDate}</li>
    <li><strong>Previous Experience:</strong> ${formData.previousExperience || "None provided"}</li>
</ul>
<hr>
<h3>Additional Notes</h3>
<p>${formData.additionalNotes || "None provided"}</p>
<hr>
<p><small>Submitted on: ${new Date().toLocaleString()}</small></p>`;

        // Confirmation message for the applicant
        const confirmationMessage = `<p>Hi ${formData.firstName},</p>
<p>Thank you for completing your onboarding information for Detail On The Go!</p>
<p>We've received all your details and will be in touch soon regarding next steps.</p>
<p>If you have any questions in the meantime, feel free to reach out to us.</p>
<p>Welcome to the team!</p>
<p>Best regards,<br/>
   Detail On The Go Team<br/>
   <a href="https://detailongo.com">detailongo.com</a>
</p>`;

        try {
            // Send internal notification email
            const internalResponse = await fetch("/api/send-email", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    fromName: `${formData.firstName} ${formData.lastName}`,
                    fromEmail: formData.email,
                    to: "<EMAIL>",
                    subject: `New Team Member Onboarding: ${formData.firstName} ${formData.lastName}`,
                    message: internalMessage,
                }),
            });

            if (!internalResponse.ok) {
                throw new Error("Failed to send internal notification");
            }

            // Send confirmation email to applicant
            try {
                await fetch("/api/send-email", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        fromName: "Detail On The Go",
                        fromEmail: "<EMAIL>",
                        to: formData.email,
                        subject: "Welcome to Detail On The Go - Onboarding Received",
                        message: confirmationMessage,
                    }),
                });
            } catch (confirmErr) {
                console.warn("Confirmation email failed:", confirmErr);
            }

            setSubmitMessage("Thank you! Your onboarding information has been submitted successfully. You should receive a confirmation email shortly.");
            
            // Reset form
            setFormData({
                firstName: "", lastName: "", email: "", phone: "", address: "", city: "", state: "", zipCode: "",
                driversLicenseNumber: "", driversLicenseState: "", ssn: "", dateOfBirth: "",
                emergencyContactName: "", emergencyContactRelationship: "", emergencyContactPhone: "", emergencyContactEmail: "",
                vehicleYear: "", vehicleMake: "", vehicleModel: "", vehicleColor: "", vehicleLicensePlate: "", vehicleInsurance: "",
                startDate: "", position: "", previousExperience: "", additionalNotes: ""
            });

        } catch (error) {
            if (error instanceof Error) {
                setSubmitMessage(`Error: ${error.message}`);
            } else {
                setSubmitMessage("An unknown error occurred. Please try again.");
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <main className="min-h-screen bg-transparent font-[Helvetica,Arial,sans-serif]">
            <section className="w-full py-12 md:pt-16 md:pb-8">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-4">
                        <ShinyText
                            text="Team Member Onboarding"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold italic text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        Welcome to Detail On The Go! Please fill out the information below to complete your onboarding process.
                    </p>
                </div>
            </section>

            {/* Onboarding Form Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-4xl mx-auto px-4">
                    <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                        <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">Onboarding Information</h2>
                        
                        {submitMessage && (
                            <div className={`text-center mb-6 p-4 rounded-lg ${submitMessage.includes("Error") ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}`}>
                                {submitMessage}
                            </div>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-8">
                            {/* Personal Information Section */}
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Personal Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">First Name *</label>
                                        <input
                                            type="text"
                                            name="firstName"
                                            value={formData.firstName}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Last Name *</label>
                                        <input
                                            type="text"
                                            name="lastName"
                                            value={formData.lastName}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Email *</label>
                                        <input
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Phone *</label>
                                        <input
                                            type="tel"
                                            name="phone"
                                            value={formData.phone}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div className="md:col-span-2">
                                        <label className="block text-gray-700 mb-2">Street Address *</label>
                                        <input
                                            type="text"
                                            name="address"
                                            value={formData.address}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">City *</label>
                                        <input
                                            type="text"
                                            name="city"
                                            value={formData.city}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">State *</label>
                                        <input
                                            type="text"
                                            name="state"
                                            value={formData.state}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">ZIP Code *</label>
                                        <input
                                            type="text"
                                            name="zipCode"
                                            value={formData.zipCode}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Identification Section */}
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Identification</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Driver's License Number *</label>
                                        <input
                                            type="text"
                                            name="driversLicenseNumber"
                                            value={formData.driversLicenseNumber}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Driver's License State *</label>
                                        <input
                                            type="text"
                                            name="driversLicenseState"
                                            value={formData.driversLicenseState}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., KS, MO, etc."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Social Security Number *</label>
                                        <input
                                            type="text"
                                            name="ssn"
                                            value={formData.ssn}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="XXX-XX-XXXX"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Emergency Contact Section */}
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Emergency Contact</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Emergency Contact Name *</label>
                                        <input
                                            type="text"
                                            name="emergencyContactName"
                                            value={formData.emergencyContactName}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Relationship *</label>
                                        <input
                                            type="text"
                                            name="emergencyContactRelationship"
                                            value={formData.emergencyContactRelationship}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., Spouse, Parent, Sibling"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Emergency Contact Phone *</label>
                                        <input
                                            type="tel"
                                            name="emergencyContactPhone"
                                            value={formData.emergencyContactPhone}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Emergency Contact Email</label>
                                        <input
                                            type="email"
                                            name="emergencyContactEmail"
                                            value={formData.emergencyContactEmail}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Vehicle Information Section */}
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Vehicle Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Vehicle Year *</label>
                                        <input
                                            type="text"
                                            name="vehicleYear"
                                            value={formData.vehicleYear}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., 2020"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Vehicle Make *</label>
                                        <input
                                            type="text"
                                            name="vehicleMake"
                                            value={formData.vehicleMake}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., Toyota, Ford, Honda"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Vehicle Model *</label>
                                        <input
                                            type="text"
                                            name="vehicleModel"
                                            value={formData.vehicleModel}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., Camry, F-150, Civic"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Vehicle Color *</label>
                                        <input
                                            type="text"
                                            name="vehicleColor"
                                            value={formData.vehicleColor}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">License Plate Number *</label>
                                        <input
                                            type="text"
                                            name="vehicleLicensePlate"
                                            value={formData.vehicleLicensePlate}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Insurance Company *</label>
                                        <input
                                            type="text"
                                            name="vehicleInsurance"
                                            value={formData.vehicleInsurance}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            placeholder="e.g., State Farm, Geico, Allstate"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Employment Information Section */}
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Employment Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Position *</label>
                                        <select
                                            name="position"
                                            value={formData.position}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        >
                                            <option value="">Select Position</option>
                                            <option value="Mobile Detailing Specialist">Mobile Detailing Specialist</option>
                                            <option value="Team Lead">Team Lead</option>
                                            <option value="Sales Representative">Sales Representative</option>
                                            <option value="Administrative">Administrative</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Expected Start Date *</label>
                                        <input
                                            type="date"
                                            name="startDate"
                                            value={formData.startDate}
                                            onChange={handleChange}
                                            className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                            required
                                        />
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <label className="block text-gray-700 mb-2">Previous Detailing/Automotive Experience</label>
                                    <textarea
                                        name="previousExperience"
                                        value={formData.previousExperience}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        rows={3}
                                        placeholder="Briefly describe any relevant experience..."
                                    />
                                </div>
                                <div className="mt-4">
                                    <label className="block text-gray-700 mb-2">Additional Notes</label>
                                    <textarea
                                        name="additionalNotes"
                                        value={formData.additionalNotes}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        rows={3}
                                        placeholder="Any additional information you'd like to share..."
                                    />
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="text-center">
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className={`group relative bg-blue-500 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:shadow-blue-500 border border-white ${isSubmitting ? "opacity-50 cursor-not-allowed" : ""}`}
                                >
                                    <span className="relative z-10 italic font-bold text-xl">
                                        {isSubmitting ? "Submitting..." : "Submit Onboarding Information"}
                                    </span>
                                    <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default OnboardingPage;

import { ImageResponse } from "next/og";

export const alt = "Service Areas | Detail On The Go";
export const contentType = "image/png";
export const size = { width: 1200, height: 630 };

export default function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f8fa",
          backgroundImage: "linear-gradient(135deg, #f5f8fa 60%, #cbe7ff 100%)",
        }}
      >
        <div
          style={{
            fontSize: "54px",
            fontWeight: "bold",
            color: "#003366",
            marginBottom: "24px",
            letterSpacing: "-1px",
          }}
        >
          Service Areas
        </div>
        <div
          style={{
            fontSize: "28px",
            color: "#1a3a5d",
            textAlign: "center",
            maxWidth: "80%",
          }}
        >
          Mobile Detailing in Lawrence, Topeka, Kansas City & More
        </div>
        <div
          style={{
            fontSize: "22px",
            color: "#2d5c88",
            marginTop: "32px",
            fontWeight: "500",
          }}
        >
          Detail On The Go
        </div>
      </div>
    ),
    size
  );
}

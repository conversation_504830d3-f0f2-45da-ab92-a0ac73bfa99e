import { 
  initializeBranchData, 
  upsertVehiclePackage,
  updateBranchPricing,
  VehiclePackage,
  VehicleAddon,
  VehicleSize 
} from './branchConfigService';
import { db } from './firebase';
import { collection, doc, setDoc, serverTimestamp } from 'firebase/firestore';

/**
 * SAFE MIGRATION SCRIPT
 * 
 * This script migrates your current hardcoded data to the new database schema
 * WITHOUT affecting existing collections (bookings, paymentLinks, users)
 * 
 * NEW COLLECTIONS CREATED:
 * - branchConfigs: Branch information and settings
 * - vehiclePackages: Global package definitions  
 * - vehicleAddons: Global addon definitions
 * - vehicleSizes: Global size definitions
 * - branchPricing: Branch-specific pricing overrides
 */

// Current hardcoded data to migrate
const MIGRATION_DATA = {
  branches: [
    {
      id: 'lwr',
      name: 'Lawrence/KC',
      businessNumber: '+***********',
      calendarId: '<EMAIL>',
      collectionId: 'sms-lwr',
      location: 'Lawrence/KC and the surrounding areas',
      coordinates: { lat: 38.9717, lng: -95.2353 },
      employeeInfo: {
        name: 'Levi Taylor',
        email: '<EMAIL>',
        number: '+***********'
      },
      reviewLink: 'https://g.page/r/CS98X9jMS0IREBM/review',
      branchLocation: '197 Pinecone Dr, Lawrence, KS 66046',
      timezone: 'America/Chicago',
      operatingHours: {
        standard: { start: '8:00 AM', end: '6:00 PM' },
        summer: { start: '7:00 AM', end: '7:00 PM' },
        winter: { start: '9:00 AM', end: '5:00 PM' }
      },
      serviceArea: {
        radius: 30,
        cities: ['Lawrence', 'Kansas City', 'Topeka', 'Overland Park']
      },
      active: true,
      enabledVehicleTypes: ['car', 'boat', 'rv']
    },
    {
      id: 'w-stl',
      name: 'West St. Louis',
      businessNumber: '+***********',
      calendarId: '<EMAIL>',
      collectionId: 'sms-e-stl',
      location: 'St. Louis and the surrounding areas',
      coordinates: { lat: 38.6270, lng: -90.1994 },
      employeeInfo: {
        name: 'Taylor Woods',
        email: '<EMAIL>',
        number: '+***********'
      },
      reviewLink: 'https://g.page/r/CaNuJ0ypIXA7EBM/review',
      branchLocation: '9220 Litzsinger Rd, St. Louis, MO 63144',
      timezone: 'America/Chicago',
      operatingHours: {
        standard: { start: '8:00 AM', end: '6:00 PM' },
        summer: { start: '7:00 AM', end: '7:00 PM' },
        winter: { start: '9:00 AM', end: '5:00 PM' }
      },
      serviceArea: {
        radius: 25,
        cities: ['St. Louis', 'Clayton', 'Ladue', 'Webster Groves']
      },
      active: true,
      enabledVehicleTypes: ['car', 'boat', 'rv']
    },
    {
      id: 'dvr',
      name: 'Denver',
      businessNumber: '+***********',
      calendarId: '<EMAIL>',
      collectionId: 'sms-dvr',
      location: 'Denver and the surrounding areas',
      coordinates: { lat: 39.7392, lng: -104.9903 },
      employeeInfo: {
        name: 'Alexis Orona',
        email: '<EMAIL>',
        number: '+***********'
      },
      reviewLink: 'https://g.page/r/CY3Q12skg1wsEBM/review',
      branchLocation: '7947 Downing St, Thornton, CO 80229',
      timezone: 'America/Denver',
      operatingHours: {
        standard: { start: '8:00 AM', end: '6:00 PM' },
        summer: { start: '7:00 AM', end: '7:00 PM' },
        winter: { start: '9:00 AM', end: '5:00 PM' }
      },
      serviceArea: {
        radius: 35,
        cities: ['Denver', 'Boulder', 'Aurora', 'Lakewood', 'Thornton']
      },
      active: true,
      enabledVehicleTypes: ['car', 'boat', 'rv']
    },
    {
      id: 'ny',
      name: 'New York',
      businessNumber: '+***********',
      calendarId: '<EMAIL>',
      collectionId: 'sms-ny',
      location: 'New York and the surrounding areas',
      coordinates: { lat: 40.7128, lng: -74.0060 },
      employeeInfo: {
        name: 'Levi Taylor',
        email: '<EMAIL>',
        number: '+***********'
      },
      reviewLink: 'https://g.page/r/CTVgBNWsI5ZKEBM/review',
      branchLocation: '32-06 30th Ave., Astoria, NY 11102',
      timezone: 'America/New_York',
      operatingHours: {
        standard: { start: '8:00 AM', end: '6:00 PM' },
        summer: { start: '7:00 AM', end: '7:00 PM' },
        winter: { start: '9:00 AM', end: '5:00 PM' }
      },
      serviceArea: {
        radius: 20,
        cities: ['New York', 'Brooklyn', 'Queens', 'Bronx', 'Manhattan']
      },
      active: true,
      enabledVehicleTypes: ['car', 'boat', 'rv']
    }
  ],

  packages: [
    {
      id: 'interior-detailing',
      name: 'Interior Detailing',
      vehicleType: 'car' as const,
      basePrice: 249, // Updated price
      description: 'Complete interior cleaning and detailing',
      images: ['/interior detailing 1.png', '/interior detailing 2.png', '/interior detailing 3.jpg'],
      estimatedDuration: 3,
      requiresConsecutiveDays: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 1
    },
    {
      id: 'exterior-detailing',
      name: 'Exterior Detailing',
      vehicleType: 'car' as const,
      basePrice: 99,
      description: 'Complete exterior wash and protection',
      images: ['/exterior wash 1.jpg', '/exterior wash 2.jpg', '/exterior wash 3.jpg', '/exterior wash 4.jpg', '/exterior wash 5.png'],
      estimatedDuration: 2,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 2
    },
    {
      id: 'interior-exterior-detailing',
      name: 'Interior & Exterior Detailing',
      vehicleType: 'car' as const,
      basePrice: 260,
      description: 'Complete interior and exterior detailing package',
      images: ['/exterior wash 5.png', '/interior detailing 3.jpg', '/exterior wash 3.jpg', '/interior detailing 2.png', '/exterior wash 4.jpg', '/interior detailing 1.png'],
      estimatedDuration: 4.5,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 3
    },
    // Boat services
    {
      id: 'boat-vacuum-wipedown',
      name: 'Vacuum & Wipedown / Pressure Wash Interior',
      vehicleType: 'boat' as const,
      basePrice: 10,
      description: 'Deep clean every corner. Refresh your boat\'s interior to like-new condition.',
      images: ['/boatinterior1.jpg', '/boat detailing 1.jpg'],
      estimatedDuration: 1,
      requiresConsecutiveDays: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl'],
      active: true,
      sortOrder: 1
    },
    {
      id: 'boat-exterior-wash',
      name: 'Boat Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry',
      vehicleType: 'boat' as const,
      basePrice: 15,
      description: 'Complete exterior care. Remove marine buildup and protect your boat\'s finish.',
      images: ['/boatbottom.jpg', '/boat washing.png'],
      estimatedDuration: 1.5,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl'],
      active: true,
      sortOrder: 2
    },
    {
      id: 'boat-deep-polish',
      name: '1 Step Deep Polish / Oxidation Removal / Heavy Algae Removal',
      vehicleType: 'boat' as const,
      basePrice: 18,
      description: 'Restore your boat\'s shine. Aggressive cleaning and protection in one service.',
      images: ['/boatpolishing.jpg', '/boat polishing 2.jpg'],
      estimatedDuration: 2,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl'],
      active: true,
      sortOrder: 3
    },
    {
      id: 'boat-ceramic-coating',
      name: 'Boat Ceramic Coating',
      vehicleType: 'boat' as const,
      basePrice: 50,
      description: 'Ultimate marine protection. Shield your boat from UV and saltwater damage.',
      images: ['/boat ceramic.png'],
      estimatedDuration: 3,
      requiresConsecutiveDays: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl'],
      active: true,
      sortOrder: 4
    },
    // RV services
    {
      id: 'rv-exterior-wash',
      name: 'RV Exterior Wash, Spray Wax & Towel Dry',
      vehicleType: 'rv' as const,
      basePrice: 15,
      description: 'Complete exterior revival. Remove dirt and protect your RV\'s finish.',
      images: ['/rv1.jpg', '/rv2.png', '/RV wash.png'],
      estimatedDuration: 2,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 1
    },
    {
      id: 'rv-roof-cleaning',
      name: 'Roof Cleaning',
      vehicleType: 'rv' as const,
      basePrice: 5,
      description: 'Comprehensive roof care. Clean and seal to prevent leaks and damage.',
      images: ['/rvroof1.jpg', '/rvroof2.jpg', '/rvroof3.jpg'],
      estimatedDuration: 1,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 2
    },
    {
      id: 'rv-deep-polish',
      name: 'One Step Deep Polish / Oxidation Removal',
      vehicleType: 'rv' as const,
      basePrice: 18,
      description: 'Restore your RV\'s original shine and protect against the elements.',
      images: ['/rv polishing.jpg'],
      estimatedDuration: 3,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 3
    }
  ],

  addons: [
    {
      id: 'window-shield-ceramic-coating',
      name: 'Window Shield Ceramic Coating',
      vehicleTypes: ['car'],
      basePrice: 25,
      hasQuantity: false,
      description: 'Get crystal-clear visibility in any weather. Our advanced coating repels water, dirt, and debris for safer, easier driving.',
      images: ['/windowceramic5.png', '/windowceramic4.png', '/windowceramic3.jpg', '/windowceramic2.jpg', '/windowceramic1.jpeg'],
      estimatedDuration: 0.5,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 1
    },
    {
      id: 'engine-clean',
      name: 'Engine Clean',
      vehicleTypes: ['car'],
      basePrice: 50,
      hasQuantity: false,
      description: 'Boost your engine\'s performance and longevity. Professional cleaning removes built-up grime and prevents potential issues.',
      images: ['/engine clean 1.JPG'],
      estimatedDuration: 1,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 2
    },
    {
      id: 'full-body-ceramic-coating',
      name: 'Full Body Ceramic Coating',
      vehicleTypes: ['car'],
      basePrice: 600,
      hasQuantity: false,
      description: 'Ultimate paint protection. Achieve a showroom shine while defending against UV rays, chemicals, and minor scratches.',
      images: ['/ceramic1.jpg', '/ceramic2.jpg', '/ceramic3.jpg', '/ceramic4.png'],
      estimatedDuration: 8,
      requiresSpecialTechnician: true,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 3
    },
    {
      id: 'scratch-removal',
      name: 'Scratch Removal / Per Body Panel',
      vehicleTypes: ['car'],
      basePrice: 200,
      hasQuantity: true,
      description: 'Restore your car\'s flawless finish. Professional scratch removal to keep your vehicle looking brand new.',
      images: ['/polishing 1.jpg', '/polishing 2.jpg'],
      estimatedDuration: 2,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 4
    },
    {
      id: 'headlight-restoration',
      name: 'Headlight Restoration',
      vehicleTypes: ['car'],
      basePrice: 70,
      hasQuantity: true,
      description: 'Bring back crystal-clear headlights. Improve night visibility and your car\'s overall appearance.',
      images: ['/headlights1.jpg', '/headlights2.jpg'],
      estimatedDuration: 1,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl'],
      active: true,
      sortOrder: 5
    },
    {
      id: 'paint-correction',
      name: 'Paint Correction',
      vehicleTypes: ['car'],
      basePrice: 600,
      hasQuantity: false,
      description: 'Eliminate imperfections for a flawless, showroom-quality finish. Perfect preparation for ceramic coating.',
      images: ['/polishing3.png'],
      estimatedDuration: 6,
      requiresSpecialTechnician: true,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 6
    }
  ],

  sizes: [
    {
      id: 'sedan',
      name: 'Sedan',
      baseMultiplier: 1.0,
      sortOrder: 1,
      active: true
    },
    {
      id: 'small-suv',
      name: 'Small SUV (2 Rows)',
      baseMultiplier: 1.1,
      sortOrder: 2,
      active: true
    },
    {
      id: 'large-suv',
      name: 'Large-SUV (3 Rows)',
      baseMultiplier: 1.15,
      sortOrder: 3,
      active: true
    },
    {
      id: 'small-truck',
      name: 'Small/Mid-Truck',
      baseMultiplier: 1.05,
      sortOrder: 4,
      active: true
    },
    {
      id: 'large-truck',
      name: 'Large-Truck',
      baseMultiplier: 1.1,
      sortOrder: 5,
      active: true
    },
    {
      id: 'van',
      name: 'Van (4+ Rows)',
      baseMultiplier: 1.7,
      sortOrder: 6,
      active: true
    }
  ]
};

/**
 * Run the complete migration
 */
export async function runSafeMigration(): Promise<{success: boolean, results: string[]}> {
  const results: string[] = [];
  let hasErrors = false;

  try {
    results.push('🚀 Starting safe database migration...');
    results.push('ℹ️  This will NOT affect existing collections (bookings, paymentLinks, users)');

    // 1. Migrate vehicle sizes (global)
    results.push('\\n📏 Migrating vehicle sizes...');
    for (const size of MIGRATION_DATA.sizes) {
      try {
        await setDoc(doc(db, 'vehicleSizes', size.id), {
          ...size,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        results.push(`✅ Created size: ${size.name}`);
      } catch (error) {
        results.push(`❌ Failed to create size ${size.name}: ${error}`);
        hasErrors = true;
      }
    }

    // 2. Migrate vehicle packages (global)
    results.push('\\n📦 Migrating vehicle packages...');
    for (const pkg of MIGRATION_DATA.packages) {
      try {
        await setDoc(doc(db, 'vehiclePackages', pkg.id), {
          ...pkg,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        results.push(`✅ Created package: ${pkg.name} ($${pkg.basePrice})`);
      } catch (error) {
        results.push(`❌ Failed to create package ${pkg.name}: ${error}`);
        hasErrors = true;
      }
    }

    // 3. Migrate vehicle addons (global)
    results.push('\\n🔧 Migrating vehicle addons...');
    for (const addon of MIGRATION_DATA.addons) {
      try {
        await setDoc(doc(db, 'vehicleAddons', addon.id), {
          ...addon,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        results.push(`✅ Created addon: ${addon.name} ($${addon.basePrice})`);
      } catch (error) {
        results.push(`❌ Failed to create addon ${addon.name}: ${error}`);
        hasErrors = true;
      }
    }

    // 4. Migrate branch configurations
    results.push('\\n🏢 Migrating branch configurations...');
    for (const branch of MIGRATION_DATA.branches) {
      try {
        await setDoc(doc(db, 'branchConfigs', branch.id), {
          ...branch,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        results.push(`✅ Created branch config: ${branch.name}`);
      } catch (error) {
        results.push(`❌ Failed to create branch config ${branch.name}: ${error}`);
        hasErrors = true;
      }
    }

    // 5. Initialize branch pricing documents (empty for now, to be managed by admin)
    results.push('\\n💰 Initializing branch pricing documents...');
    for (const branch of MIGRATION_DATA.branches) {
      try {
        await setDoc(doc(db, 'branchPricing', branch.id), {
          id: branch.id,
          packagePricing: {},
          addonPricing: {},
          sizeMultipliers: {},
          updatedAt: serverTimestamp(),
          updatedBy: 'migration-script'
        });
        results.push(`✅ Initialized pricing for: ${branch.name}`);
      } catch (error) {
        results.push(`❌ Failed to initialize pricing for ${branch.name}: ${error}`);
        hasErrors = true;
      }
    }

    results.push('\\n🎉 Migration completed!');
    results.push('\\n📋 Summary:');
    results.push(`   • Created ${MIGRATION_DATA.sizes.length} vehicle sizes`);
    results.push(`   • Created ${MIGRATION_DATA.packages.length} vehicle packages`);
    results.push(`   • Created ${MIGRATION_DATA.addons.length} vehicle addons`);
    results.push(`   • Created ${MIGRATION_DATA.branches.length} branch configurations`);
    results.push(`   • Initialized ${MIGRATION_DATA.branches.length} branch pricing documents`);
    results.push('\\n✅ Your existing collections (bookings, paymentLinks, users) are untouched!');

    return { success: !hasErrors, results };

  } catch (error) {
    results.push(`\\n❌ Migration failed with error: ${error}`);
    return { success: false, results };
  }
}

/**
 * Test function to verify the migration worked
 */
export async function testMigration(): Promise<{success: boolean, results: string[]}> {
  const results: string[] = [];
  
  try {
    results.push('🧪 Testing migration results...');
    
    // Test that we can fetch branch config for Lawrence
    const { getBranchConfig, getBranchPackages, getBranchVehicleSizes } = await import('./branchConfigService');
    
    const lwrConfig = await getBranchConfig('lwr');
    if (lwrConfig) {
      results.push(`✅ Successfully fetched Lawrence branch config: ${lwrConfig.name}`);
    } else {
      results.push('❌ Failed to fetch Lawrence branch config');
      return { success: false, results };
    }
    
    const lwrPackages = await getBranchPackages('lwr', 'car');
    results.push(`✅ Successfully fetched ${lwrPackages.length} packages for Lawrence`);
    lwrPackages.forEach(pkg => {
      results.push(`   • ${pkg.name}: $${pkg.basePrice}`);
    });
    
    const lwrSizes = await getBranchVehicleSizes('lwr');
    results.push(`✅ Successfully fetched ${lwrSizes.length} vehicle sizes for Lawrence`);
    
    results.push('\\n🎉 Migration test passed! Database is working correctly.');
    return { success: true, results };
    
  } catch (error) {
    results.push(`❌ Migration test failed: ${error}`);
    return { success: false, results };
  }
}

/**
 * Console helper for running migration
 */
export async function runMigrationFromConsole() {
  console.log('🚀 Starting database migration...');
  
  const migrationResult = await runSafeMigration();
  migrationResult.results.forEach(result => console.log(result));
  
  if (migrationResult.success) {
    console.log('\\n🧪 Running test...');
    const testResult = await testMigration();
    testResult.results.forEach(result => console.log(result));
    
    if (testResult.success) {
      console.log('\\n🎉 Migration and test completed successfully!');
      console.log('\\n📝 Next steps:');
      console.log('   1. Update your BookingSection component to use the new database service');
      console.log('   2. Build admin interface to manage branch-specific pricing');
      console.log('   3. Test the customer booking flow');
    }
  }
}

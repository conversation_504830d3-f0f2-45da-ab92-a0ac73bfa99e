.outer-container {
    display: flex;
    min-height: 100%;
    flex: 1 1 0%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

@media (min-width: 640px) {
    .outer-container {
        aspect-ratio: 4 / 3;
    }
}

@media (min-width: 768px) {
    .outer-container {
        aspect-ratio: 2 / 1;
    }
}

.step-circle-container {
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 28rem;
    border-radius: 2rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    background: linear-gradient(to bottom, #93C5FD, #3B82F6);
    /* Match TiltedCard */
    color: white;
}

.step-indicator-row {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 2rem;
}

.step-content-default {
    position: relative;
    overflow: hidden;
}

.step-default {
    padding-left: 2rem;
    padding-right: 2rem;
}

.footer-container {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-bottom: 2rem;
}

.footer-nav {
    margin-top: 2.5rem;
    display: flex;
}

.footer-nav.spread {
    justify-content: space-between;
}

.footer-nav.end {
    justify-content: flex-end;
}

.back-button {
    transition: all 350ms;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    color: #ffffff;
    cursor: pointer;
    border: 1px solid white;
}

.back-button:hover {
    color: #e0e0e0;
}

.back-button.inactive {
    pointer-events: none;
    opacity: 0.5;
    color: #a3a3a3;
}

.next-button {
    transition: all 350ms;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    background: linear-gradient(to top, #0044ff, #008cff);
    /* Match your scheme */
    color: white;
    /* White text */
    font-weight: 500;
    letter-spacing: -0.025em;
    padding: 0.375rem 0.875rem;
    cursor: pointer;
}

.next-button:hover {
    background: linear-gradient(to top, #0033cc, #0077e6);
    /* Slightly darker for hover */
}

.next-button:active {
    background: linear-gradient(to top, #002299, #0066d9);
    /* Even darker for active */
}

.step-indicator {
    position: relative;
    cursor: pointer;
    outline: none;
}

.step-indicator-inner {
    display: flex;
    height: 2rem;
    width: 2rem;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    font-weight: 600;
}

.active-dot {
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 9999px;
    background-color: white;
    /* White dot for active step */
}

.step-number {
    font-size: 0.875rem;
}

.step-connector {
    position: relative;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    height: 0.125rem;
    flex: 1;
    overflow: hidden;
    border-radius: 0.25rem;
    background-color: #52525b;
    /* Inactive connector */
}

.step-connector-inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background: linear-gradient(to right, #0044ff, #008cff);
    /* Gradient for completed connector */
}
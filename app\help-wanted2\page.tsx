"use client";
import Image from "next/image";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";

const FleetWashingPage = () => {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        phone: "",
        email: "",
        message: "",
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        const message = `
            <h2>Fleet Washing Interest</h2>
            <h3>Contact Information</h3>
            <ul>
                <li><strong>First Name:</strong> ${formData.firstName}</li>
                <li><strong>Last Name:</strong> ${formData.lastName}</li>
                <li><strong>Phone:</strong> ${formData.phone}</li>
                <li><strong>Email:</strong> ${formData.email}</li>
            </ul>
            <h3>Message</h3>
            <p>${formData.message || "No additional message provided"}</p>
        `;

        try {
            const response = await fetch("/api/send-email", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    fromName: `${formData.firstName} ${formData.lastName}`,
                    fromEmail: formData.email,
                    to: "<EMAIL>",
                    subject: "Fleet Washing Interest - Detail On The Go",
                    message,
                }),
            });

            if (!response.ok) {
                throw new Error("Failed to send email");
            }

            const result = await response.json();
            setSubmitMessage(result.message);
            setFormData({
                firstName: "", lastName: "", phone: "", email: "", message: ""
            });
        } catch (error) {
            if (error instanceof Error) {
                setSubmitMessage(`Error: ${error.message}`);
            } else {
                setSubmitMessage("An unknown error occurred.");
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <main className="min-h-screen bg-transparent font-[Helvetica,Arial,sans-serif]">
            <section className="w-full py-12 md:pt-16 md:pb-8">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-4">
                        <ShinyText
                            text="Fleet Washing Opportunity"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold italic text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        Easy part-time work washing fleet vehicles every other Sunday.
                    </p>

                </div>
            </section>

            {/* Job Details Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Fleet Washing Details</h2>
                    <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">What You'll Do</h3>
                                <ul className="list-disc list-inside text-gray-700 space-y-2">
                                    <li>Wash the outside of 7 fleet vehicles</li>
                                    <li>Each vehicle takes approximately 15-20 minutes to wash and dry</li>
                                    <li>Work every other Sunday (2x monthly)</li>
                                    <li>Year-round opportunity (weather permitting)</li>
                                    <li>Takes around 2 hours per visit</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">What We Provide</h3>
                                <ul className="list-disc list-inside text-gray-700 space-y-2">
                                    <li>All equipment provided</li>
                                    <li>Complete training included</li>
                                    <li>$10 per vehicle ($70 per visit, $140 monthly)</li>
                                    <li>Option for additional work</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div className="mt-8 p-6 bg-pink-50 rounded-lg border border-pink-200">
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Perfect For:</h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="text-center">
                                    <div className="text-2xl mb-2">💰</div>
                                    <p className="text-gray-700 font-medium">Extra Income</p>
                                    <p className="text-sm text-gray-600">Easy way to earn $140/month</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl mb-2">⏰</div>
                                    <p className="text-gray-700 font-medium">Flexible Schedule</p>
                                    <p className="text-sm text-gray-600">Only 4 hours per month</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl mb-2">🚀</div>
                                    <p className="text-gray-700 font-medium">Growth Potential</p>
                                    <p className="text-sm text-gray-600">Option for more work</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Locations Section */}
            <section className="w-full py-16 bg-blue-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">Available Locations</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <div className="text-center">
                                <div className="text-4xl mb-4">📍</div>
                                <h3 className="text-2xl font-semibold mb-2 text-white">Salina, KS</h3>
                                <p className="text-gray-200 mb-4">Looking for 1 person</p>
                                <div className="bg-pink-500 text-white px-4 py-2 rounded-lg font-semibold">
                                    Position Available
                                </div>
                            </div>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <div className="text-center">
                                <div className="text-4xl mb-4">📍</div>
                                <h3 className="text-2xl font-semibold mb-2 text-white">Hays, KS</h3>
                                <p className="text-gray-200 mb-4">Looking for 1 person</p>
                                <div className="bg-pink-500 text-white px-4 py-2 rounded-lg font-semibold">
                                    Position Available
                                </div>
                            </div>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <div className="text-center">
                                <div className="text-4xl mb-4">📍</div>
                                <h3 className="text-2xl font-semibold mb-2 text-white">Lawrence, KS</h3>
                                <p className="text-gray-200 mb-4">Looking for 1 person</p>
                                <div className="bg-pink-500 text-white px-4 py-2 rounded-lg font-semibold">
                                    Position Available
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Apply Section */}
            <section className="w-full py-16 bg-gray-200/90 backdrop-blur-xl" id="apply-here">
                <div className="max-w-4xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Express Your Interest</h2>
                    <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                        <p className="text-gray-700 mb-6 text-center">
                            If you or someone you know might be interested in this fleet washing opportunity, please fill out the form below.
                        </p>
                        
                        {submitMessage && (
                            <p className={`text-center mb-4 ${submitMessage.includes("Error") ? "text-red-600" : "text-green-600"}`}>
                                {submitMessage}
                            </p>
                        )}
                        
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-gray-700 mb-2 font-medium">First Name *</label>
                                    <input
                                        type="text"
                                        name="firstName"
                                        value={formData.firstName}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-gray-700 mb-2 font-medium">Last Name *</label>
                                    <input
                                        type="text"
                                        name="lastName"
                                        value={formData.lastName}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        required
                                    />
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-gray-700 mb-2 font-medium">Phone *</label>
                                    <input
                                        type="tel"
                                        name="phone"
                                        value={formData.phone}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-gray-700 mb-2 font-medium">Email *</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                        required
                                    />
                                </div>
                            </div>
                            
                            <div>
                                <label className="block text-gray-700 mb-2 font-medium">Additional Message (Optional)</label>
                                <textarea
                                    name="message"
                                    value={formData.message}
                                    onChange={handleChange}
                                    className="w-full p-3 border border-gray-300 rounded-lg text-black"
                                    rows={4}
                                    placeholder="Any questions or additional information..."
                                />
                            </div>
                            
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className={`w-full group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:shadow-pink-500 border border-white ${isSubmitting ? "opacity-50 cursor-not-allowed" : ""}`}
                            >
                                <span className="relative z-10 italic font-bold text-xl">
                                    {isSubmitting ? "Submitting..." : "Submit Interest"}
                                </span>
                                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </button>
                        </form>
                    </div>
                </div>
            </section>
        </main>
    );
};

export default FleetWashingPage;


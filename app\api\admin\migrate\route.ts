import { NextResponse } from 'next/server';
import { runSafeMigration, testMigration } from '@/lib/firebase/migrationScript';

/**
 * API endpoint to safely migrate hardcoded data to database
 * 
 * This endpoint:
 * 1. Migrates hardcoded pricing/config data to new Firebase collections
 * 2. Does NOT affect existing collections (bookings, paymentLinks, users)
 * 3. Creates safe fallback system for customer-facing app
 * 4. Updates Interior Detailing price to $249 for all branches
 * 
 * Usage: POST /api/admin/migrate
 */
export async function POST(request: Request) {
  try {
    // Optional: Add admin authentication check here
    // const { user } = await authenticateAdmin(request);
    // if (!user || !user.isAdmin) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    console.log('🚀 Starting database migration...');
    
    // Run the migration
    const migrationResult = await runSafeMigration();
    
    if (!migrationResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Migration failed',
        details: migrationResult.results
      }, { status: 500 });
    }

    // Test the migration
    console.log('🧪 Testing migration...');
    const testResult = await testMigration();
    
    return NextResponse.json({
      success: true,
      message: 'Migration completed successfully',
      migration: migrationResult.results,
      test: testResult.results,
      summary: {
        newCollections: [
          'branchConfigs (4 branches)',
          'vehiclePackages (3 packages)', 
          'vehicleAddons (6 addons)',
          'vehicleSizes (6 sizes)',
          'branchPricing (4 branch pricing docs)'
        ],
        priceUpdates: [
          'Interior Detailing: $239 → $249 (all branches)'
        ],
        existingCollectionsUntouched: [
          'bookings',
          'paymentLinks', 
          'users'
        ]
      }
    });

  } catch (error) {
    console.error('Migration API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Migration failed with error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check migration status
 */
export async function GET() {
  try {
    // Check if migration has been run by testing data availability
    const { getBranchConfig } = await import('@/lib/firebase/branchConfigService');
    
    const lwrConfig = await getBranchConfig('lwr');
    const hasMigrated = lwrConfig && 'operatingHours' in lwrConfig;
    
    return NextResponse.json({
      migrated: hasMigrated,
      message: hasMigrated 
        ? 'Database migration has been completed'
        : 'Database migration has not been run yet',
      instructions: hasMigrated 
        ? 'You can now build the admin interface to manage pricing'
        : 'Run POST /api/admin/migrate to migrate hardcoded data to database'
    });

  } catch (error) {
    return NextResponse.json({
      migrated: false,
      message: 'Could not determine migration status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

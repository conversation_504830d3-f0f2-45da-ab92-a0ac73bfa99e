import { db } from './firebase';
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy,
  QueryDocumentSnapshot,
  DocumentData 
} from 'firebase/firestore';

// Types for branch-specific configurations
export interface BranchInfo {
  id: string;
  name: string;
  businessNumber: string;
  calendarId: string;
  collectionId: string;
  location: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  employeeInfo: {
    name: string;
    email: string;
    number: string;
  };
  reviewLink: string;
  branchLocation: string;
  operatingHours: {
    standard: { start: string; end: string };
    summer: { start: string; end: string };
    winter: { start: string; end: string };
  };
  serviceArea: {
    radius: number;
    cities: string[];
  };
  active: boolean;
  timezone: string;
}

export interface VehicleType {
  id: string;
  name: string;
  available: boolean;
  hasSizes: boolean;
  hasPackages: boolean;
  sortOrder: number;
}

export interface Package {
  id: string;
  vehicleType: string;
  name: string;
  basePrice: number;
  description: string;
  images: string[];
  available: boolean;
  sortOrder: number;
  type: 'package' | 'service';
  estimatedDuration: number;
  requiresConsecutiveDays: boolean;
  weatherDependent: boolean;
}

export interface Addon {
  id: string;
  vehicleTypes: string[];
  name: string;
  price: number;
  hasQuantity: boolean;
  description: string;
  images: string[];
  available: boolean;
  sortOrder: number;
  estimatedDuration: number;
  requiresSpecialTechnician: boolean;
  weatherDependent: boolean;
}

export interface VehicleSize {
  id: string;
  name: string;
  multiplier: number;
  sortOrder: number;
  available: boolean;
}

// Cache for branch configurations
const branchCache = new Map<string, {
  data: any;
  timestamp: number;
  expiry: number;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper function to check cache
function getCachedData<T>(key: string): T | null {
  const cached = branchCache.get(key);
  if (cached && Date.now() < cached.expiry) {
    return cached.data as T;
  }
  return null;
}

// Helper function to set cache
function setCachedData<T>(key: string, data: T): void {
  branchCache.set(key, {
    data,
    timestamp: Date.now(),
    expiry: Date.now() + CACHE_DURATION
  });
}

// Fallback data structure (matches current hardcoded structure)
const fallbackVehicleConfig = {
  car: {
    sizes: ["Sedan", "Small SUV (2 Rows)", "Large-SUV (3 Rows)", "Small/Mid-Truck", "Large-Truck", "Van (4+ Rows)"],
    sizeMultipliers: [1, 1.1, 1.15, 1.05, 1.1, 1.7],
    packages: [
      {
        name: "Interior Detailing",
        price: 239,
        images: ["/interior detailing 1.png", "/interior detailing 2.png", "/interior detailing 3.jpg"],
        available: true
      },
      {
        name: "Exterior Detailing", 
        price: 99,
        images: ["/exterior wash 1.jpg", "/exterior wash 2.jpg", "/exterior wash 3.jpg"],
        available: true
      },
      {
        name: "Interior & Exterior Detailing",
        price: 260,
        images: ["/exterior wash 5.png", "/interior detailing 3.jpg"],
        available: true
      }
    ],
    addons: [
      {
        name: "Window Shield Ceramic Coating",
        price: 25,
        hasQuantity: false,
        description: "Get crystal-clear visibility in any weather.",
        images: ["/windowceramic5.png"],
        available: true
      }
    ]
  },
  boat: {
    services: [],
    addons: []
  },
  rv: {
    services: [],
    addons: []
  }
};

/**
 * Get branch information
 */
export async function getBranchInfo(branchId: string): Promise<BranchInfo | null> {
  const cacheKey = `branch_${branchId}`;
  const cached = getCachedData<BranchInfo>(cacheKey);
  if (cached) return cached;

  try {
    const branchDoc = await getDoc(doc(db, 'branches', branchId));
    if (branchDoc.exists()) {
      const data = { id: branchDoc.id, ...branchDoc.data() } as BranchInfo;
      setCachedData(cacheKey, data);
      return data;
    }
  } catch (error) {
    console.error(`Error fetching branch ${branchId}:`, error);
  }
  
  return null;
}

/**
 * Get available vehicle types for a branch
 */
export async function getBranchVehicleTypes(branchId: string): Promise<VehicleType[]> {
  const cacheKey = `vehicleTypes_${branchId}`;
  const cached = getCachedData<VehicleType[]>(cacheKey);
  if (cached) return cached;

  try {
    const q = query(
      collection(db, 'branches', branchId, 'vehicleTypes'),
      where('available', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const snapshot = await getDocs(q);
    const vehicleTypes = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as VehicleType[];
    
    setCachedData(cacheKey, vehicleTypes);
    return vehicleTypes;
  } catch (error) {
    console.error(`Error fetching vehicle types for branch ${branchId}:`, error);
    // Return default vehicle types as fallback
    return [
      { id: 'car', name: 'Car', available: true, hasSizes: true, hasPackages: true, sortOrder: 1 }
    ];
  }
}

/**
 * Get packages/services for a specific vehicle type and branch
 */
export async function getBranchPackages(branchId: string, vehicleType: string): Promise<Package[]> {
  const cacheKey = `packages_${branchId}_${vehicleType}`;
  const cached = getCachedData<Package[]>(cacheKey);
  if (cached) return cached;

  try {
    const q = query(
      collection(db, 'branches', branchId, 'packages'),
      where('vehicleType', '==', vehicleType),
      where('available', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const snapshot = await getDocs(q);
    const packages = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Package[];
    
    setCachedData(cacheKey, packages);
    return packages;
  } catch (error) {
    console.error(`Error fetching packages for branch ${branchId}, vehicle ${vehicleType}:`, error);
    
    // Return fallback data based on vehicle type
    if (vehicleType === 'car' && fallbackVehicleConfig.car.packages) {
      return fallbackVehicleConfig.car.packages.map((pkg, index) => ({
        id: pkg.name.toLowerCase().replace(/\s+/g, '-'),
        vehicleType: 'car',
        name: pkg.name,
        basePrice: pkg.price,
        description: '',
        images: pkg.images,
        available: pkg.available,
        sortOrder: index,
        type: 'package' as const,
        estimatedDuration: 4,
        requiresConsecutiveDays: false,
        weatherDependent: false
      }));
    }
    
    return [];
  }
}

/**
 * Get addons for a specific vehicle type and branch
 */
export async function getBranchAddons(branchId: string, vehicleType?: string): Promise<Addon[]> {
  const cacheKey = `addons_${branchId}_${vehicleType || 'all'}`;
  const cached = getCachedData<Addon[]>(cacheKey);
  if (cached) return cached;

  try {
    let q;
    if (vehicleType) {
      q = query(
        collection(db, 'branches', branchId, 'addons'),
        where('vehicleTypes', 'array-contains', vehicleType),
        where('available', '==', true),
        orderBy('sortOrder', 'asc')
      );
    } else {
      q = query(
        collection(db, 'branches', branchId, 'addons'),
        where('available', '==', true),
        orderBy('sortOrder', 'asc')
      );
    }
    
    const snapshot = await getDocs(q);
    const addons = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Addon[];
    
    setCachedData(cacheKey, addons);
    return addons;
  } catch (error) {
    console.error(`Error fetching addons for branch ${branchId}, vehicle ${vehicleType}:`, error);
    
    // Return fallback addons for cars
    if (vehicleType === 'car' && fallbackVehicleConfig.car.addons) {
      return fallbackVehicleConfig.car.addons.map((addon, index) => ({
        id: addon.name.toLowerCase().replace(/\s+/g, '-'),
        vehicleTypes: ['car'],
        name: addon.name,
        price: addon.price,
        hasQuantity: addon.hasQuantity,
        description: addon.description,
        images: addon.images,
        available: addon.available,
        sortOrder: index,
        estimatedDuration: 0.5,
        requiresSpecialTechnician: false,
        weatherDependent: false
      }));
    }
    
    return [];
  }
}

/**
 * Get vehicle sizes for a branch (car-specific)
 */
export async function getBranchVehicleSizes(branchId: string): Promise<VehicleSize[]> {
  const cacheKey = `vehicleSizes_${branchId}`;
  const cached = getCachedData<VehicleSize[]>(cacheKey);
  if (cached) return cached;

  try {
    const q = query(
      collection(db, 'branches', branchId, 'vehicleSizes'),
      where('available', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const snapshot = await getDocs(q);
    const sizes = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as VehicleSize[];
    
    setCachedData(cacheKey, sizes);
    return sizes;
  } catch (error) {
    console.error(`Error fetching vehicle sizes for branch ${branchId}:`, error);
    
    // Return fallback sizes
    return fallbackVehicleConfig.car.sizes.map((size, index) => ({
      id: size.toLowerCase().replace(/\s+/g, '-'),
      name: size,
      multiplier: fallbackVehicleConfig.car.sizeMultipliers[index],
      sortOrder: index,
      available: true
    }));
  }
}

/**
 * Get complete vehicle configuration for a branch
 */
export async function getBranchVehicleConfig(branchId: string) {
  try {
    const [vehicleTypes, carPackages, carAddons, vehicleSizes] = await Promise.all([
      getBranchVehicleTypes(branchId),
      getBranchPackages(branchId, 'car'),
      getBranchAddons(branchId, 'car'),
      getBranchVehicleSizes(branchId)
    ]);

    return {
      vehicleTypes,
      car: {
        packages: carPackages,
        addons: carAddons,
        sizes: vehicleSizes
      }
    };
  } catch (error) {
    console.error(`Error fetching complete vehicle config for branch ${branchId}:`, error);
    // Return fallback configuration
    return {
      vehicleTypes: [
        { id: 'car', name: 'Car', available: true, hasSizes: true, hasPackages: true, sortOrder: 1 }
      ],
      car: {
        packages: [],
        addons: [],
        sizes: []
      }
    };
  }
}

/**
 * Clear cache for a specific branch or all cache
 */
export function clearBranchCache(branchId?: string): void {
  if (branchId) {
    // Clear specific branch cache
    const keysToDelete = Array.from(branchCache.keys()).filter(key => 
      key.includes(branchId)
    );
    keysToDelete.forEach(key => branchCache.delete(key));
  } else {
    // Clear all cache
    branchCache.clear();
  }
}

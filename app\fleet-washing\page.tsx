"use client";
import Link from "next/link";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";

export default function FleetWashingPage() {
    const [submitMessage, setSubmitMessage] = useState("");

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);

        try {
            const response = await fetch('/api/contact', {
                method: 'POST',
                body: formData,
            });

            if (response.ok) {
                setSubmitMessage("Thank you! We'll contact you within 24 hours with a custom fleet washing quote.");
                (e.target as HTMLFormElement).reset();
            } else {
                setSubmitMessage("Error submitting form. Please try again or call us directly.");
            }
        } catch (error) {
            setSubmitMessage("Error submitting form. Please try again or call us directly.");
        }
    };

    return (
        <main className="min-h-screen bg-transparent font-[Helvetica,Arial,sans-serif]">
            {/* Hero Section */}
            <section className="w-full py-12 md:pt-16 md:pb-8">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-4">
                        <ShinyText
                            text="Fleet Washing & Mobile Detailing"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold italic text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        Here at Detail On The Go, we're passionate about <strong>making your fleet look absolutely amazing</strong>. Our mobile fleet washing brings professional results directly to your location—saving time and ensuring your vehicles always make the right impression.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <Link
                            href="#get-quote"
                            className="inline-block bg-pink-600 hover:bg-pink-700 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg transition-colors"
                        >
                            Get Free Quote
                        </Link>
                        <div className="text-white/80 text-sm">
                            ⭐ Over a decade of experience • Self-sufficient mobile service
                        </div>
                    </div>
                </div>
            </section>

            {/* Why Choose Us Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-4 text-gray-800">Why Choose Detail On The Go Fleet Services?</h2>
                    <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
                        When you choose us, you're not just getting a wash—you're getting people who truly care about your fleet as much as you do.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md text-center">
                            <div className="text-4xl mb-4">🚛</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Professional Image</h3>
                            <p className="text-gray-700">Clean vehicles reflect your company's attention to detail and professionalism to every customer.</p>
                        </div>
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md text-center">
                            <div className="text-4xl mb-4">⏰</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Time Savings</h3>
                            <p className="text-gray-700">We come to your location, so your drivers stay productive while we handle the cleaning.</p>
                        </div>
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md text-center">
                            <div className="text-4xl mb-4">🌿</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Eco-Friendly</h3>
                            <p className="text-gray-700">From our soaps to our methods, every product we use is eco-friendly and high-performing for lasting results.</p>
                        </div>
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md text-center">
                            <div className="text-4xl mb-4">🎯</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Completely Self-Sufficient</h3>
                            <p className="text-gray-700">We bring our own water and electricity—no disruption to your operations, just professional results.</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Services Section */}
            <section className="w-full py-16 bg-gray-100/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Fleet Services We Offer</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Exterior Fleet Washing</h3>
                            <ul className="list-disc list-inside text-gray-700 space-y-2">
                                <li>Complete exterior wash and rinse</li>
                                <li>Professional tire cleaning and dressing</li>
                                <li>Window cleaning (exterior)</li>
                                <li>Chrome and metal polishing</li>
                                <li>Spot-free drying and protection</li>
                                <li>Paint protection and coating options</li>
                            </ul>
                        </div>
                        <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Interior Fleet Detailing</h3>
                            <ul className="list-disc list-inside text-gray-700 space-y-2">
                                <li>Thorough vacuuming of seats and floors</li>
                                <li>Dashboard and console cleaning</li>
                                <li>Interior window cleaning</li>
                                <li>Sanitization and deodorizing</li>
                                <li>Floor mat deep cleaning</li>
                                <li>Leather conditioning (when applicable)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            {/* Industries We Serve */}
            <section className="w-full py-16 bg-gray-800/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-4 text-white">Industries We're Advancing</h2>
                    <p className="text-center text-white/80 mb-12">
                        From delivery trucks to construction equipment, we service fleets across all industries
                    </p>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 text-center">
                        {[
                            { title: "Transportation", icon: "🚚" },
                            { title: "Construction", icon: "🚜" },
                            { title: "Landscaping", icon: "🌱" },
                            { title: "Energy", icon: "⚡" },
                            { title: "Waste Management", icon: "♻️" },
                            { title: "Fleet Sales/Leasing", icon: "🏢" }
                        ].map((item, index) => (
                            <div key={index} className="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                                <div className="text-3xl mb-3">{item.icon}</div>
                                <p className="text-white font-medium text-sm">{item.title}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Fleet Types Section */}
            <section className="w-full py-16 bg-pink-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">Fleet Types We Service</h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                        {[
                            { icon: "🚚", title: "Delivery Trucks" },
                            { icon: "🚐", title: "Service Vans" },
                            { icon: "🚗", title: "Company Cars" },
                            { icon: "🚛", title: "Box Trucks" },
                            { icon: "🚌", title: "Buses" },
                            { icon: "🚜", title: "Construction Equipment" },
                            { icon: "🚑", title: "Emergency Vehicles" },
                            { icon: "🏢", title: "Corporate Fleets" }
                        ].map((item, index) => (
                            <div key={index} className="bg-white/20 backdrop-blur-sm p-4 rounded-xl">
                                <div className="text-3xl mb-2">{item.icon}</div>
                                <p className="text-white font-medium">{item.title}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Scheduling Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-4 text-gray-800">Flexible Scheduling Options</h2>
                    <p className="text-center text-gray-600 mb-12">
                        We work around your schedule to minimize downtime and maximize convenience
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span className="text-2xl">📅</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Weekly Service</h3>
                            <p className="text-gray-700">Regular weekly cleaning to maintain consistent fleet appearance and professional image.</p>
                        </div>
                        <div className="text-center">
                            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span className="text-2xl">📆</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Bi-Weekly Service</h3>
                            <p className="text-gray-700">Cost-effective bi-weekly cleaning perfect for most fleet operations and budgets.</p>
                        </div>
                        <div className="text-center">
                            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span className="text-2xl">⚡</span>
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">On-Demand</h3>
                            <p className="text-gray-700">Flexible scheduling for special events, seasonal needs, or as-needed cleaning.</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Guarantees Section */}
            <section className="w-full py-16 bg-blue-50/90 backdrop-blur-xl">
                <div className="max-w-4xl mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-8 text-gray-800">Our Fleet Service Guarantee</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md">
                            <div className="text-3xl mb-4">💯</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">30-Day Money Back Guarantee</h3>
                            <p className="text-gray-700">Not satisfied with our service? We'll make it right or refund your money.</p>
                        </div>
                        <div className="bg-white/80 backdrop-blur-lg p-6 rounded-xl border border-white/30 shadow-md">
                            <div className="text-3xl mb-4">📋</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">No Long-Term Contracts</h3>
                            <p className="text-gray-700">Flexible service agreements that work for your business, not against it.</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Quote Form Section */}
            <section className="w-full py-16 bg-gray-200/90 backdrop-blur-xl" id="get-quote">
                <div className="max-w-4xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-4 text-gray-800">Get Your Free Fleet Washing Quote</h2>
                    <p className="text-center text-gray-600 mb-8">
                        Tell us about your fleet and we'll provide a custom quote within 24 hours. No pressure, just honest pricing.
                    </p>
                    <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">

                        {submitMessage && (
                            <p className={`text-center mb-4 ${submitMessage.includes("Error") ? "text-red-600" : "text-green-600"}`}>
                                {submitMessage}
                            </p>
                        )}

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                                        Company Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="company"
                                        name="company"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="contact" className="block text-sm font-medium text-gray-700 mb-2">
                                        Contact Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="contact"
                                        name="contact"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                        Email Address *
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                                        Phone Number *
                                    </label>
                                    <input
                                        type="tel"
                                        id="phone"
                                        name="phone"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label htmlFor="fleet-size" className="block text-sm font-medium text-gray-700 mb-2">
                                        Number of Vehicles *
                                    </label>
                                    <select
                                        id="fleet-size"
                                        name="fleet-size"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    >
                                        <option value="">Select fleet size</option>
                                        <option value="1-5">1-5 vehicles</option>
                                        <option value="6-10">6-10 vehicles</option>
                                        <option value="11-25">11-25 vehicles</option>
                                        <option value="26-50">26-50 vehicles</option>
                                        <option value="50+">50+ vehicles</option>
                                    </select>
                                </div>
                                <div>
                                    <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                                        Service Location *
                                    </label>
                                    <input
                                        type="text"
                                        id="location"
                                        name="location"
                                        placeholder="City, State"
                                        required
                                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                    />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="vehicle-types" className="block text-sm font-medium text-gray-700 mb-2">
                                    Vehicle Types
                                </label>
                                <textarea
                                    id="vehicle-types"
                                    name="vehicle-types"
                                    rows={3}
                                    placeholder="Describe your fleet (e.g., delivery trucks, service vans, company cars)"
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                ></textarea>
                            </div>

                            <div>
                                <label htmlFor="frequency" className="block text-sm font-medium text-gray-700 mb-2">
                                    Preferred Service Frequency
                                </label>
                                <select
                                    id="frequency"
                                    name="frequency"
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                >
                                    <option value="">Select frequency</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="bi-weekly">Bi-weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="as-needed">As needed</option>
                                </select>
                            </div>

                            <div>
                                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                                    Additional Details
                                </label>
                                <textarea
                                    id="message"
                                    name="message"
                                    rows={4}
                                    placeholder="Any specific requirements, timing preferences, or questions?"
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                ></textarea>
                            </div>

                            <button
                                type="submit"
                                className="w-full bg-pink-600 hover:bg-pink-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors text-lg"
                            >
                                Get My Free Quote
                            </button>
                        </form>
                    </div>
                </div>
            </section>

        </main>
    );
}

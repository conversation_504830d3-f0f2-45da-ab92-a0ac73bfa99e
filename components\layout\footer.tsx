"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { useDocument } from "react-firebase-hooks/firestore";
import { doc } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { auth, db } from "@/lib/firebase/firebase";
import { signInWithGoogle, firebaseSignOut } from "@/lib/firebase/auth";
import BookingSectionWithStripe from "@/components/BookingSection";

export default function Footer() {
  const [user, loading] = useAuthState(auth);
  const [mounted, setMounted] = useState(false);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const router = useRouter();

  const [userDoc, userDocLoading] = useDocument(
    user ? doc(db, "users", user.uid) : null
  );

  useEffect(() => {
    setMounted(true);
  }, []);

  const dashboardLink = userDoc?.data()?.isAdmin ? "/admin" : "/time";
  const isAdmin = userDoc?.data()?.isAdmin || false;

  const handleSignOut = async () => {
    await firebaseSignOut();
    router.push("/");
  };

  return (
    <footer className="bg-[#00072D] text-white pt-12 pb-6 border-t border-[#001C55] font-sans">
      <div className="max-w-7xl mx-auto px-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="flex flex-col items-center">
            <h3 className="font-bold text-2xl mb-4 text-white font-sans">
              <span className="italic">DETAIL ON THE GO</span>
            </h3>
            <p className="text-[#7CB9E8] text-sm font-sans">
              Here at Detail On The Go, we're passionate about two things:{" "}
              <span className="font-bold text-pink-500">
                making your vehicle look absolutely amazing
              </span>{" "}
              and{" "}
              <span className="font-bold text-pink-500">
                creating a place where everyone loves to work
              </span>
              . We take pride in your maintaining vehicle paintwork and protecting
              it from whatever the envoirenment throws its way. When you choose
              us, you're not just getting a detail—you're getting people who
              truly care about your car as much as you do.
            </p>
            <div className="flex space-x-4 mt-4">
              <a
                href="https://facebook.com"
                className="text-[#7CB9E8] hover:text-white transition-colors"
                aria-label="Facebook"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="https://instagram.com"
                className="text-[#7CB9E8] hover:text-white transition-colors"
                aria-label="Instagram"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="https://tiktok.com"
                className="text-[#7CB9E8] hover:text-white transition-colors"
                aria-label="TikTok"
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.55 2.89 2.89 0 01.88.13V9.4a6.34 6.34 0 00-1-.09A6.34 6.34 0 004 15.74a6.34 6.34 0 0011.27 4 6.34 6.34 0 001.1-3.55V9.64a8.27 8.27 0 004.07 1V7.14a4.83 4.83 0 01-1.84-.45z" />
                </svg>
              </a>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 font-sans">
              Our Services
            </h3>
            <ul className="text-[#B6D0E2] text-sm space-y-2 font-sans">
              <li>
                <a
                  href="/services#interior"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Interior Detailing
                </a>
              </li>
              <li>
                <a
                  href="/services#exterior"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Exterior Detailing
                </a>
              </li>
              <li>
                <a
                  href="/services#paint"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Paint Correction
                </a>
              </li>
              <li>
                <a
                  href="/services#ceramic"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Ceramic Coating
                </a>
              </li>
              <li>
                <a
                  href="/services#pdr"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Paintless Dent Removal
                </a>
              </li>
              <li>
                <a
                  href="/rv-boat"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Boat & RV Detailing
                </a>
              </li>
              <li>
                <a
                  href="/fleet-washing"
                  className="hover:underline hover:text-white transition-colors"
                >
                  Fleet Washing
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-3 font-sans">
              Hours & Information
            </h3>
            <ul className="text-[#B6D0E2] text-sm space-y-2 font-sans">
              <li>
                <button
                  onClick={() => setShowBookingModal(true)}
                  className="text-[#B6D0E2] hover:text-white hover:underline transition-colors font-sans"
                >
                  <span className="mr-2">🔍</span> Find Your Detailer
                </button>
              </li>
              <li>
                <button
                  onClick={() => setShowBookingModal(true)}
                  className="text-[#B6D0E2] hover:text-white hover:underline transition-colors font-sans"
                >
                  <span className="mr-2">📅</span> Book Now
                </button>
              </li>
              <li>
                <Link
                  href="/help-wanted"
                  className="text-[#B6D0E2] hover:text-white hover:underline transition-colors font-sans"
                >
                  <span className="mr-2">💼</span> Join Our Detailing Team
                </Link>
              </li>
              <li>
                <Link
                  href="/franchise"
                  className="text-[#B6D0E2] hover:text-white hover:underline transition-colors font-sans"
                >
                  <span className="mr-2">🚀</span> Start Your Detailing Business
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-4">
          <span className="font-semibold">Franchise Locations:</span>
          <ul className="flex flex-wrap gap-4 mt-2 text-sm">
            <li>
              <a
                href="/franchise/kansas-city"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Kansas City
              </a>
            </li>
            <li>
              <a
                href="/franchise/dallas-fort-worth"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Dallas-Fort Worth
              </a>
            </li>
            <li>
              <a
                href="/franchise/houston"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Houston
              </a>
            </li>
            <li>
              <a
                href="/franchise/denver"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Denver
              </a>
            </li>
            <li>
              <a
                href="/franchise/phoenix"
                className="underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                Phoenix
              </a>
            </li>
          </ul>
        </div>

        <div className="mt-8 text-[#7CB9E8] text-xs font-sans">
          <p>
            © 2023 Detail on the Go, LLC |{" "}
            <a
              href="/privacy"
              className="hover:underline hover:text-white transition-colors"
            >
              Privacy Policy
            </a>{" "}
            |{" "}
            <a
              href="/terms"
              className="hover:underline hover:text-white transition-colors"
            >
              Terms & Conditions
            </a>
          </p>
        </div>
      </div>

      {showBookingModal && (
        <BookingSectionWithStripe onClose={() => setShowBookingModal(false)} />
      )}
    </footer>
  );
}

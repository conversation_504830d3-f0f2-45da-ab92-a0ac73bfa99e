import { useState, useEffect } from "react";
import {
    format,
    startOfMonth,
    endOfMonth,
    eachDayOfInterval,
    isBefore,
    addMonths,
    subMonths,
    getDay,
} from "date-fns";
import { DateTime } from 'luxon';

// Define branch timezones with type safety
const branchTimezones = {
    "lwr": "America/Chicago",
    "w-stl": "America/Chicago",
    "dvr": "America/Denver",
    "ny": "America/New_York"
} as const;

type Branch = keyof typeof branchTimezones;

interface AvailabilitySlot {
    isoString: string;
    formattedTime: string;
}

interface SchedulingPageProps {
    branch?: Branch;
    onSelectSlot: (date: string, time: string) => void;
    selectedDate?: string;
    selectedTime?: string;
}

const SchedulingPage = ({
    branch = "lwr",
    onSelectSlot,
    selectedDate: propSelectedDate,
    selectedTime: propSelectedTime,
}: SchedulingPageProps) => {

    const [availableSlots, setAvailableSlots] = useState<AvailabilitySlot[]>([]);
    const [selectedSlot, setSelectedSlot] = useState<string>("");
    const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
    const [selectedDate, setSelectedDate] = useState<string | null>(propSelectedDate || null);
    const [isLoading, setIsLoading] = useState(true);
    const [periods, setPeriods] = useState(".");

    useEffect(() => {
        if (propSelectedDate) {
            setSelectedDate(propSelectedDate);
        }
    }, [propSelectedDate]);

    const fetchAvailability = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(
                `https://booking-website-availability-896343340170.us-central1.run.app?branch=${branch}`
            );

            if (!response.ok) throw new Error("Failed to fetch availability");

            const data: string[] = await response.json();
            const formattedSlots = data.map((iso) => {
                const branchTime = DateTime.fromISO(iso)
                    .setZone(branchTimezones[branch])
                    .toFormat('h:mm a');

                return {
                    isoString: iso,
                    formattedTime: branchTime
                };
            });

            setAvailableSlots(formattedSlots);
        } catch (error) {
            console.error("Error fetching availability:", error);
            setAvailableSlots([]);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchAvailability();
    }, [currentMonth, branch]);

    useEffect(() => {
        if (isLoading) {
            const interval = setInterval(() => {
                setPeriods((prev) => {
                    if (prev === ".") return "..";
                    if (prev === "..") return "...";
                    return ".";
                });
            }, 1000);
            return () => clearInterval(interval);
        }
    }, [isLoading]);

    useEffect(() => {
        if (availableSlots.length === 0) return;

        const currentMonthStart = startOfMonth(currentMonth);
        const currentMonthEnd = endOfMonth(currentMonth);

        let hasSlotsInCurrentMonth = false;

        for (const slot of availableSlots) {
            try {
                const slotDateTime = DateTime.fromISO(slot.isoString).setZone(branchTimezones[branch]);
                if (slotDateTime && typeof slotDateTime.isValid === 'boolean' && slotDateTime.isValid) {
                    const slotJSDate = slotDateTime.toJSDate();
                    if (slotJSDate >= currentMonthStart && slotJSDate <= currentMonthEnd) {
                        hasSlotsInCurrentMonth = true;
                        break;
                    }
                }
            } catch (e) {
                console.error("Error processing date:", e);
                continue;
            }
        }

        if (!hasSlotsInCurrentMonth) {
            let earliestDate: DateTime | null = null;

            for (const slot of availableSlots) {
                try {
                    const dateTime = DateTime.fromISO(slot.isoString).setZone(branchTimezones[branch]);
                    if (dateTime && typeof dateTime.isValid === 'boolean' && dateTime.isValid) {
                        if (!earliestDate || dateTime < earliestDate) {
                            earliestDate = dateTime;
                        }
                    }
                } catch (e) {
                    console.error("Error processing date:", e);
                    continue;
                }
            }

            if (earliestDate && typeof earliestDate.isValid === 'boolean' && earliestDate.isValid) {
                try {
                    const earliestMonth = startOfMonth(earliestDate.toJSDate());
                    if (currentMonth.getTime() !== earliestMonth.getTime()) {
                        setCurrentMonth(earliestMonth);
                    }
                } catch (e) {
                    console.error("Error processing earliest month:", e);
                }
            }
        }
    }, [availableSlots, branch, currentMonth]);

    const generateCalendarDates = () => {
        const start = startOfMonth(currentMonth);
        const end = endOfMonth(currentMonth);
        const daysInMonth = eachDayOfInterval({ start, end });
        const firstDayOfMonth = getDay(start);

        const paddingDays = Array.from({ length: firstDayOfMonth }, () => null);

        const calendarDays = [
            ...paddingDays,
            ...daysInMonth.map((date) => {
                const branchDateTime = DateTime.fromJSDate(date).setZone(branchTimezones[branch]);
                const formatted = branchDateTime.toFormat("yyyy-MM-dd");
                const todayInBranch = DateTime.now().setZone(branchTimezones[branch]).startOf('day');
                const isPastDate = branchDateTime.startOf('day') < todayInBranch;

                const hasSlots = availableSlots.some(slot =>
                    DateTime.fromISO(slot.isoString)
                        .setZone(branchTimezones[branch])
                        .toISODate() === branchDateTime.toISODate()
                );

                return {
                    date,
                    formatted,
                    isCurrentMonth: true,
                    isDisabled: isPastDate || !hasSlots,
                    isPastDate,
                    isFullyBooked: !isPastDate && !hasSlots,
                };
            })
        ];
        return calendarDays;
    };

    const handlePrevMonth = () => {
        setCurrentMonth(subMonths(currentMonth, 1));
        setSelectedDate(null);
        setSelectedSlot("");
    };

    const handleNextMonth = () => {
        setCurrentMonth(addMonths(currentMonth, 1));
        setSelectedDate(null);
        setSelectedSlot("");
    };

    const handleDateSelect = (formatted: string | null, isDisabled: boolean) => {
        if (formatted && !isDisabled) {
            setSelectedDate(formatted);
            setSelectedSlot("");
            setTimeout(() => {
                document.getElementById("available-times")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
        }
    };

    const handleSlotSelect = (slot: AvailabilitySlot) => {
        setSelectedSlot(slot.isoString);
        onSelectSlot(slot.isoString.split("T")[0], slot.formattedTime);
        setTimeout(() => {
            document.getElementById("frequency-section")?.scrollIntoView({ behavior: "smooth" });
        }, 100);
    };

    const calendarDates = generateCalendarDates();

    return (
        <div className="scheduling-page w-full grid grid-cols-1 gap-4 relative">
            <div className="calendar-header flex items-center justify-between mb-4 sm:mb-6 gap-1 sm:gap-2">
                <button
                    onClick={handlePrevMonth}
                    className="px-2 sm:px-4 py-1 sm:py-2 text-sm sm:text-base bg-blue-50 text-blue-900 rounded sm:rounded-lg hover:bg-blue-100"
                >
                    ←
                </button>

                <h2 className="text-base sm:text-lg font-bold text-blue-900 bg-blue-50 px-3 py-1 rounded-md">
                    {format(currentMonth, "MMMM yyyy")}
                </h2>

                <button
                    onClick={handleNextMonth}
                    className="px-2 sm:px-4 py-1 sm:py-2 text-sm sm:text-base bg-blue-50 text-blue-900 rounded sm:rounded-lg hover:bg-blue-100"
                >
                    →
                </button>
            </div>

            <div className="w-full relative">
                {isLoading && (
                    <div className="absolute inset-0 z-10 flex items-center justify-center bg-blue-500">
                        <p className="text-white text-2xl font-semibold">Getting availability{periods}</p>
                    </div>
                )}
                <div className="calendar-grid w-full grid grid-cols-7 gap-px bg-blue-50 rounded-lg overflow-hidden">
                    {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                        <div
                            key={day}
                            className="calendar-day-header bg-blue-50 p-2 sm:p-3 text-center text-xs sm:text-sm font-medium text-blue-600"
                        >
                            {day}
                        </div>
                    ))}

                    {calendarDates.map((day, index) => (
                        <button
                            key={`${index}-${day?.formatted || 'padding'}`}
                            disabled={!day || day.isDisabled}
                            onClick={() => day && handleDateSelect(day.formatted, day.isDisabled)}
                            className={`aspect-square p-1 sm:p-3 text-xs sm:text-sm ${!day
                                ? "bg-gray-200 text-gray-500 cursor-default"
                                : selectedDate === day.formatted
                                    ? "bg-blue-600 text-white"
                                    : day.isDisabled
                                        ? day.isFullyBooked
                                            ? "bg-red-100 text-red-900 cursor-not-allowed"
                                            : "bg-gray-400 text-gray-700 cursor-not-allowed"
                                        : "bg-white text-blue-900 hover:bg-blue-50"
                                }`}
                        >
                            {day ? (
                                <div className="flex flex-col items-center justify-center h-full">
                                    <span className="text-xs sm:text-sm">
                                        {DateTime.fromJSDate(day.date)
                                            .setZone(branchTimezones[branch])
                                            .toFormat("d")}
                                    </span>
                                    {day.isFullyBooked && (
                                        <span className="text-[8px] sm:text-[10px] font-bold text-red-900 leading-tight">
                                            FULL
                                        </span>
                                    )}
                                </div>
                            ) : ""}
                        </button>
                    ))}
                </div>

                {selectedDate && (
                    <div
                        id="available-times"
                        className="time-slots mt-0 bg-white sm:rounded-lg p-2 sm:p-4 border-0 sm:border border-blue-100 w-full"
                    >
                        <h3 className="text-sm sm:text-lg font-semibold text-blue-900 mb-2 sm:mb-4">
                            Available Times
                        </h3>

                        <div className="time-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3">
                            {availableSlots
                                .filter((slot) => slot.isoString.startsWith(selectedDate))
                                .map((slot) => (
                                    <button
                                        key={slot.isoString}
                                        onClick={() => handleSlotSelect(slot)}
                                        className={`text-sm sm:text-base p-2 sm:p-3 rounded-sm sm:rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 ${selectedSlot === slot.isoString
                                            ? "bg-blue-600 text-white"
                                            : "bg-white text-blue-900 border border-blue-200"
                                            }`}
                                    >
                                        {slot.formattedTime}
                                    </button>
                                ))}
                        </div>

                        <div className="mt-3 p-3 text-base text-yellow-900 bg-yellow-50 rounded-md border border-yellow-100 break-words">
                            ⚠️ Please note: Our 12:00 PM start times are estimated—our morning job may finish sooner or run long, so we aim to arrive on time but could be a bit earlier or later.
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default SchedulingPage;
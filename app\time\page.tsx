"use client";
export const dynamic = "force-dynamic";

import { useAuthState } from "react-firebase-hooks/auth";
import { useCollection, useDocument } from "react-firebase-hooks/firestore";
import { collection, query, where, addDoc, updateDoc, deleteDoc, doc } from "firebase/firestore";
import { auth, db } from "@/lib/firebase/firebase";
import { useState, useEffect, useRef, RefObject } from "react";
import { startOfWeek, eachDayOfInterval, addDays, isSameDay, format, differenceInMinutes } from "date-fns";
import { Timestamp } from "firebase/firestore";
import { Trash2, Pencil } from "lucide-react";

interface TimeEntry {
    id: string;
    userId: string;
    date: Timestamp;
    startTime: string;
    endTime: string;
    tookBreak?: boolean;
    breakMinutes?: number;
    hours?: string;
    wage: number;
}

interface User {
    uid: string;
}

export default function TimePage() {
    const [user, loading, error] = useAuthState(auth);
    const [wageDoc, wageLoading, wageError] = useDocument(user ? doc(db, "users", user.uid) : null);

    useEffect(() => {
        console.log("TimePage - Component mounted on client");
    }, []);

    if (loading) return <div className="text-center text-gray-500 py-20">Loading authentication...</div>;
    if (error) return <div className="text-center text-red-500 py-20">Auth Error: {error.message}</div>;
    if (!user) return <div className="text-center text-gray-500 py-20">Please sign in to view this page.</div>;

    const wage = wageDoc?.exists() ? (wageDoc.data()?.wage as number || 0) : 0;

    return <TimeTrackerContent user={user} wage={wage} />;
}

interface TimeTrackerContentProps {
    user: User;
    wage: number;
}

function TimeTrackerContent({ user, wage }: TimeTrackerContentProps) {
    const q = query(collection(db, "employeeTime"), where("userId", "==", user.uid));
    const [snapshot, snapshotLoading, snapshotError] = useCollection(q);
    const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
    const [startTime, setStartTime] = useState("08:00");
    const [endTime, setEndTime] = useState("15:00");
    const [tookBreak, setTookBreak] = useState(false);
    const [breakMinutes, setBreakMinutes] = useState(0);
    const [editingId, setEditingId] = useState<string | null>(null);
    const formRef: RefObject<HTMLFormElement> = useRef(null);

    if (snapshotLoading) return <div className="text-center text-gray-500 py-20">Loading time data...</div>;
    if (snapshotError) return <div className="text-center text-red-500 py-20">Error: {snapshotError.message}</div>;

    const entries: TimeEntry[] = snapshot?.docs.map((doc) => ({ id: doc.id, ...doc.data() } as TimeEntry)) || [];

    const weeks: Record<string, TimeEntry[]> = {};
    entries.forEach((entry) => {
        const date = entry.date.toDate();
        const weekStart = startOfWeek(date, { weekStartsOn: 0 });
        const weekKey = weekStart.toISOString();
        if (!weeks[weekKey]) weeks[weekKey] = [];
        weeks[weekKey].push(entry);
    });
    const sortedWeekKeys = Object.keys(weeks).sort((a, b) => b.localeCompare(a));

    const calculateHours = (): string => {
        const start = new Date(`${date}T${startTime}:00`);
        const end = new Date(`${date}T${endTime}:00`);
        const totalMinutes = differenceInMinutes(end, start);
        const breakTime = tookBreak ? breakMinutes : 0;
        return Math.max((totalMinutes - breakTime) / 60, 0).toFixed(1);
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const hours = parseFloat(calculateHours());
        if (!hours || hours <= 0) {
            alert("Invalid time duration");
            return;
        }

        const entryDate = new Date(`${date}T00:00:00`);
        const entryData: Omit<TimeEntry, 'id'> = {
            userId: user.uid,
            date: Timestamp.fromDate(entryDate),
            startTime,
            endTime,
            tookBreak,
            breakMinutes: tookBreak ? breakMinutes : 0,
            hours: calculateHours(),
            wage: wage,
        };

        try {
            if (editingId) {
                await updateDoc(doc(db, "employeeTime", editingId), entryData);
                setEditingId(null);
            } else {
                await addDoc(collection(db, "employeeTime"), entryData);
            }
            resetForm();
        } catch (error) {
            console.error("Error saving time entry:", error);
            alert("Failed to save time entry");
        }
    };

    const handleEdit = (entry: TimeEntry) => {
        setEditingId(entry.id);
        setDate(format(entry.date.toDate(), "yyyy-MM-dd"));
        setStartTime(entry.startTime);
        setEndTime(entry.endTime);
        setTookBreak(entry.tookBreak ?? false);
        setBreakMinutes(entry.breakMinutes ?? 0);
        formRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
    };

    const handleDelete = async (id: string) => {
        if (confirm("Are you sure you want to delete this entry?")) {
            try {
                await deleteDoc(doc(db, "employeeTime", id));
            } catch (error) {
                console.error("Error deleting time entry:", error);
                alert("Failed to delete time entry");
            }
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setDate(new Date().toISOString().split("T")[0]);
        setStartTime("08:00");
        setEndTime("15:00");
        setTookBreak(false);
        setBreakMinutes(0);
    };

    return (
        <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-800">Time Tracker</h1>
                <p className="text-lg text-gray-600">Current Wage: <span className="font-semibold">${wage.toFixed(2)}/hr</span></p>
            </div>

            <form ref={formRef} onSubmit={handleSubmit} className="mb-8 bg-gray-50 p-6 rounded-lg shadow-inner">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                        <input
                            type="date"
                            value={date}
                            onChange={(e) => setDate(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                        <input
                            type="time"
                            value={startTime}
                            onChange={(e) => setStartTime(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                        <input
                            type="time"
                            value={endTime}
                            onChange={(e) => setEndTime(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>
                    <div className="flex items-center gap-4">
                        <label className="block text-sm font-medium text-gray-700">Took a Break?</label>
                        <input
                            type="checkbox"
                            checked={tookBreak}
                            onChange={(e) => setTookBreak(e.target.checked)}
                            className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        {tookBreak && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Break (minutes)</label>
                                <input
                                    type="number"
                                    min="0"
                                    value={breakMinutes}
                                    onChange={(e) => setBreakMinutes(parseInt(e.target.value) || 0)}
                                    className="w-20 p-2 border border-guttergray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        )}
                    </div>
                </div>
                <div className="mt-6 flex justify-between items-center">
                    <p className="text-gray-600">Total Hours: <span className="font-semibold">{calculateHours()}</span></p>
                    <div className="flex gap-4">
                        {editingId && (
                            <button
                                type="button"
                                onClick={resetForm}
                                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition"
                            >
                                Cancel
                            </button>
                        )}
                        <button
                            type="submit"
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
                        >
                            {editingId ? "Update" : "Submit"}
                        </button>
                    </div>
                </div>
            </form>

            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Weekly Summaries</h2>
            {sortedWeekKeys.length === 0 ? (
                <p className="text-red-500 text-center">No time entries yet. Start by submitting your hours above!</p>
            ) : (
                sortedWeekKeys.map((weekKey) => (
                    <WeeklySummary
                        key={weekKey}
                        weekStart={new Date(weekKey)}
                        entries={weeks[weekKey]}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                ))
            )}
        </div>
    );
}

interface WeeklySummaryProps {
    weekStart: Date;
    entries: TimeEntry[];
    onEdit: (entry: TimeEntry) => void;
    onDelete: (id: string) => void;
}

function WeeklySummary({ weekStart, entries, onEdit, onDelete }: WeeklySummaryProps) {
    const days = eachDayOfInterval({ start: weekStart, end: addDays(weekStart, 6) });
    const dailyTotals = days.map((day) => {
        const dayEntries = entries.filter((entry) => isSameDay(entry.date.toDate(), day));
        const totalHours = dayEntries.reduce((sum: number, entry: TimeEntry) => sum + parseFloat(entry.hours || "0"), 0);
        const dailyWage = dayEntries.reduce((sum: number, entry: TimeEntry) => sum + (parseFloat(entry.hours || "0") * entry.wage), 0);
        return { day, totalHours, dailyWage, entries: dayEntries };
    });
    const weeklyTotalHours = dailyTotals.reduce((sum: number, { totalHours }: { totalHours: number }) => sum + totalHours, 0);
    const weeklyTotalWage = dailyTotals.reduce((sum: number, { dailyWage }: { dailyWage: number }) => sum + dailyWage, 0);
    const overtimeHours = Math.max(weeklyTotalHours - 40, 0);
    const overtimeWage = overtimeHours * entries[0]?.wage * 1.5; // Default 1.5x rate, using first entry's wage

    return (
        <div className="mb-8 bg-gray-50 p-4 rounded-lg shadow-md">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Week of {format(weekStart, "MMMM d, yyyy")}</h3>
            <table className="w-full border-collapse">
                <thead>
                    <tr className="bg-gray-200">
                        <th className="border p-2 text-left text-gray-700">Day</th>
                        <th className="border p-2 text-left text-gray-700">Start</th>
                        <th className="border p-2 text-left text-gray-700">End</th>
                        <th className="border p-2 text-left text-gray-700">Wage</th>
                        <th className="border p-2 text-left text-gray-700">Break (min)</th>
                        <th className="border p-2 text-right text-gray-700">Hours</th>
                        <th className="border p-2 text-right text-gray-700">Daily Wage</th>
                        <th className="border p-2 text-right text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {dailyTotals.map(({ day, totalHours, dailyWage, entries }) =>
                        entries.map((entry) => (
                            <tr key={entry.id} className="hover:bg-gray-100">
                                <td className="border p-2">{format(day, "EEEE (M/d)")}</td>
                                <td className="border p-2">{entry.startTime}</td>
                                <td className="border p-2">{entry.endTime}</td>
                                <td className="border p-2">${entry.wage.toFixed(2)}</td>
                                <td className="border p-2">{entry.tookBreak ? entry.breakMinutes : "0"}</td>
                                <td className="border p-2 text-right">{entry.hours}</td>
                                <td className="border p-2 text-right">${(parseFloat(entry.hours || "0") * entry.wage).toFixed(2)}</td>
                                <td className="border p-2 text-right">
                                    <button
                                        onClick={() => onEdit(entry)}
                                        className="text-blue-600 hover:text-blue-800 mr-2"
                                    >
                                        <Pencil className="h-4 w-4" />
                                    </button>
                                    <button
                                        onClick={() => onDelete(entry.id)}
                                        className="text-red-600 hover:text-red-800"
                                    >
                                        <Trash2 className="h-4 w-4" />
                                    </button>
                                </td>
                            </tr>
                        ))
                    )}
                    <tr className="font-semibold bg-gray-100">
                        <td className="border p-2" colSpan={5}>Total Hours</td>
                        <td className="border p-2 text-right">{weeklyTotalHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${weeklyTotalWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                    <tr className="font-semibold bg-gray-100">
                        <td className="border p-2" colSpan={5}>Overtime</td>
                        <td className="border p-2 text-right">{overtimeHours.toFixed(1)}</td>
                        <td className="border p-2 text-right">${overtimeWage.toFixed(2)}</td>
                        <td className="border p-2"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}
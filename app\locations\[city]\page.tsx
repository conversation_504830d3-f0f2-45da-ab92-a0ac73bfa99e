import { notFound } from 'next/navigation';
import { locations, Branch } from '@/components/locations';
import dynamic from 'next/dynamic';

export async function generateMetadata({ params }: { params: { city: string } }) {
    const branch = locations[params.city];
    if (!branch) return notFound();
    return {
        title: `${branch.location} Mobile Detailing | Detail On The Go`,
        description: `Professional mobile detailing services in ${branch.location} - Interior & Exterior Detailing, Ceramic Coating, and more.`,
        openGraph: {
            title: `Professional Car Detailing in ${branch.location} | Detail On The Go`,
            description: `Expert mobile car detailing services delivered to your location in ${branch.location}. Book online today!`,
            images: ['/social-preview.jpg'],
        },
    };
}

export async function generateStaticParams() {
    return Object.keys(locations).map((city) => ({ city }));
}

// Explicitly type the dynamic import so it expects a prop "branch" of type Branch
interface LocalHomeClientProps {
    branch: Branch;
}

const LocalHomeClient = dynamic<LocalHomeClientProps>(
    () => import('./LocalHomeClient'),
    { ssr: false }
);

export default function LocalPage({ params }: { params: { city: string } }) {
    const branch = locations[params.city];
    if (!branch) notFound();
    return <LocalHomeClient branch={branch} />;
}
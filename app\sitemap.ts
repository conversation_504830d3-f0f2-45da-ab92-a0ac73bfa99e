import { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://www.detailongo.com";

  // Franchise city pages
  const franchiseCities = [
    'kansas-city',
    'dallas-fort-worth',
    'houston',
    'denver',
    'phoenix'
  ].map((city) => ({
    url: `${baseUrl}/franchise/${city}/`,
    lastModified: new Date(),
  }));

  return [
    {
      url: `${baseUrl}/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/services/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/rv-boat/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/contact/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/faq/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/franchise/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/help-wanted/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/fleet-washing/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/privacy/`,
      lastModified: new Date(),
    },
    {
      url: `${baseUrl}/terms/`,
      lastModified: new Date(),
    },
    ...franchiseCities
  ];
}
"use client";
import React, { useState } from "react";
import ShinyText from "@/components/ShinyText";

interface FranchiseApplicationPopupProps {
    onClose: () => void;
}

interface ApplicationFormData {
    contact: {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        cityState: string;
    };
    business: {
        investmentCapacity: string;
        vehicleOwnership: string;
        experience: string;
        timeline: string;
        pathInterest: string;
    };
    motivation: {
        findUs: string;
    };

}

const FranchiseApplicationPopup: React.FC<FranchiseApplicationPopupProps> = ({ onClose }) => {
    const [currentStep, setCurrentStep] = useState(1);
    const [vehicleOwnership, setVehicleOwnership] = useState<string | null>(null);
    const [pathInterest, setPathInterest] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);

    const [formData, setFormData] = useState<ApplicationFormData>({
        contact: {
            firstName: "",
            lastName: "",
            email: "",
            phone: "",
            cityState: "",
        },
        business: {
            investmentCapacity: "",
            vehicleOwnership: "",
            experience: "",
            timeline: "",
            pathInterest: "",
        },
        motivation: {
            findUs: "",

        },
    });

    const steps = [
        { title: "Contact Info", fields: ["firstName", "lastName", "email", "phone", "cityState"] },
        { title: "Business Basics", fields: ["investmentCapacity", "vehicleOwnership", "experience", "timeline"] },
        { title: "Final Details", fields: ["weeklyHours", "startTimeline", "otherCommitments", "findUs"] },
    ];

    const formatEmailMessageForApi = (data: ApplicationFormData): string => {
        const html = `
            <h2 style="color: #333;">New Franchise Application Received</h2>
            <hr>
            <h3>Contact Information</h3>
            <ul>
                <li><strong>Name:</strong> ${data.contact.firstName || 'N/A'} ${data.contact.lastName || ''}</li>
                <li><strong>Email:</strong> ${data.contact.email || 'N/A'}</li>
                <li><strong>Phone:</strong> ${data.contact.phone || 'N/A'}</li>
                <li><strong>City/State:</strong> ${data.contact.cityState || 'N/A'}</li>
            </ul>
            <hr>
            <h3>Business Details</h3>
            <ul>
                <li><strong>Investment Capacity:</strong> ${data.business.investmentCapacity || 'N/A'}</li>
                <li><strong>Has Vehicle:</strong> ${data.business.vehicleOwnership || 'N/A'}</li>
                <li><strong>Experience:</strong> ${data.business.experience || 'N/A'}</li>
                <li><strong>Target Launch Timeline:</strong> ${data.business.timeline || 'N/A'}</li>
                <li><strong>Path Interest:</strong> ${data.business.pathInterest || 'N/A'}</li>
            </ul>
            <hr>
            <h3>Motivation</h3>
  <li><strong>Where found us:</strong> ${data.motivation.findUs || 'N/A'}</li>
            <hr>
            <p><small>Submitted on: ${new Date().toLocaleString()}</small></p>
        `;
        return html;
    };

    const formatConfirmationHtml = (data: ApplicationFormData): string => `
  <p>Hi ${data.contact.firstName},</p>
  <p>Thanks for your interest in Detail On The Go! We’ve received your application and will review it shortly.</p>
  <p>What’s next:</p>
  <ul>
    <li>We’ll be in touch within 3–5 business days with an update.</li>
    <li>If approved, we’ll schedule a quick call to go over details.</li>
  </ul>
  <p>Looking forward to exploring this opportunity with you!</p>
  <p>Best regards,<br/>
     Detail On The Go Team<br/>
     <a href="https://detailongo.com">detailongo.com</a>
  </p>
`;

    const handleVehicleSelection = (value: string) => {
        if (!isSubmitting) {
            setVehicleOwnership(value);
            setFormData({
                ...formData,
                business: { ...formData.business, vehicleOwnership: value },
            });
        }
    };

    const handlePathSelection = (value: string) => {
        if (!isSubmitting) {
            setPathInterest(value);
            setFormData({
                ...formData,
                business: { ...formData.business, pathInterest: value },
            });
        }
    };

    const handleInputChange = (section: keyof ApplicationFormData, field: string, value: string) => {
        setFormData({
            ...formData,
            [section]: { ...formData[section], [field]: value },
        });
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (isSubmitting) return;

        setIsSubmitting(true);
        setSubmitError(null);

        // Attempt to add email to subscriber list
        try {
            const subscriberData = { email: formData.contact.email };
            const addSubscriberUrl = process.env.NEXT_PUBLIC_ADD_SUBSCRIBER;
            if (!addSubscriberUrl) {
                throw new Error("NEXT_PUBLIC_ADD_SUBSCRIBER is not defined");
            }
            const subscriberResponse = await fetch(addSubscriberUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(subscriberData),
            });
            if (!subscriberResponse.ok) {
                console.error("Failed to add subscriber:", subscriberResponse.statusText);
            }
        } catch (err) {
            console.error("Subscription error:", err);
        }

        // Proceed with existing email sending logic
        try {
            const subject = `New Franchise Application: ${formData.contact.firstName} ${formData.contact.lastName}`;
            const message = formatEmailMessageForApi(formData);
            const fromName = `${formData.contact.firstName} ${formData.contact.lastName}`;
            const fromEmail = formData.contact.email;
            const internalTo = "<EMAIL>";

            const internalPayload = {
                fromName,
                fromEmail,
                to: internalTo,
                subject,
                message,
            };

            const internalRes = await fetch("/api/send-email", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(internalPayload),
            });

            if (!internalRes.ok) {
                let errMsg = `Internal email failed: ${internalRes.status}`;
                try {
                    const errJson = await internalRes.json();
                    errMsg = errJson.details || errJson.error || errMsg;
                } catch { }
                throw new Error(errMsg);
            }

            const confirmationHtml = formatConfirmationHtml(formData);
            const applicantPayload = {
                fromName: "Detail On The Go",
                fromEmail: "<EMAIL>",
                to: formData.contact.email,
                subject: "Thank you for applying to Detail On The Go!",
                message: confirmationHtml,
            };

            try {
                await fetch("/api/send-email", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(applicantPayload),
                });
            } catch (confirmErr) {
                console.warn("Confirmation email failed:", confirmErr);
            }

            setCurrentStep(4); // Move to confirmation slide
        } catch (err) {
            console.error("Submission Error:", err);
            setSubmitError(err instanceof Error ? err.message : "An unexpected error occurred.");
        } finally {
            setIsSubmitting(false);
        }
    };

    const ProgressBar = () => (
        <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
            <div
                className="bg-pink-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${Math.min((currentStep / steps.length) * 100, 100)}%` }}
            />
        </div>
    );

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-screen flex flex-col overflow-hidden border …">
                <div className="bg-gradient-to-r from-palatinate_blue to-blue-700 p-6">
                    <div className="flex flex-col mb-4">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-3">
                                <img
                                    src="/mascot-face.png"
                                    alt="Mascot Logo"
                                    className="w-8 h-8"
                                />
                                <ShinyText
                                    text="Get on the List"
                                    speed={5}
                                    className="text-3xl font-bold text-white"
                                />
                            </div>
                            <button
                                onClick={onClose}
                                className="text-white hover:text-pink-300 transition-colors text-xl"
                                disabled={isSubmitting}
                            >
                                ✕
                            </button>
                        </div>
                        <p className="mt-2 text-sm text-gray-200">
                            Limited availability. Complete our brief application to get started.
                        </p>
                    </div>

                    <ProgressBar />
                </div>

                {currentStep < 4 ? (
                    <form onSubmit={handleSubmit} className="p-6 space-y-8 flex-grow overflow-y-auto"
                    >

                        {currentStep === 1 && (
                            <div className="space-y-4">
                                <h3 className="text-xl font-semibold text-gray-800">Contact Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <input
                                        type="text"
                                        placeholder="First Name"
                                        className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 text-gray-800"
                                        value={formData.contact.firstName}
                                        onChange={(e) => handleInputChange('contact', 'firstName', e.target.value)}
                                        required
                                        disabled={isSubmitting}
                                    />
                                    <input
                                        type="text"
                                        placeholder="Last Name"
                                        className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 text-gray-800"
                                        value={formData.contact.lastName}
                                        onChange={(e) => handleInputChange('contact', 'lastName', e.target.value)}
                                        required
                                        disabled={isSubmitting}
                                    />
                                    <input
                                        type="email"
                                        placeholder="Email"
                                        className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 text-gray-800"
                                        value={formData.contact.email}
                                        onChange={(e) => handleInputChange('contact', 'email', e.target.value)}
                                        required
                                        disabled={isSubmitting}
                                    />
                                    <input
                                        type="tel"
                                        placeholder="Phone"
                                        className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 text-gray-800"
                                        value={formData.contact.phone}
                                        onChange={(e) => handleInputChange('contact', 'phone', e.target.value)}
                                        required
                                        disabled={isSubmitting}
                                    />
                                    <input
                                        type="text"
                                        placeholder="City & State"
                                        className="p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 md:col-span-2 text-gray-800"
                                        value={formData.contact.cityState}
                                        onChange={(e) => handleInputChange('contact', 'cityState', e.target.value)}
                                        required
                                        disabled={isSubmitting}
                                    />
                                </div>
                            </div>
                        )}

                        {currentStep === 2 && (
                            <div className="space-y-6">
                                <h3 className="text-xl font-semibold text-gray-800">Business Details</h3>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block mb-2 font-medium text-gray-700">Investment Capacity</label>
                                        <select
                                            className="w-full p-3 border rounded-lg bg-white text-gray-800 focus:ring-2 focus:ring-pink-500 border-gray-300"
                                            value={formData.business.investmentCapacity}
                                            onChange={(e) => handleInputChange('business', 'investmentCapacity', e.target.value)}
                                            disabled={isSubmitting}
                                        >
                                            <option value="">Select investment range</option>
                                            <option value="5k-15k">Less than $500</option>
                                            <option value="5k-15k">$500 - $1k</option>
                                            <option value="5k-15k">$1k - $5k</option>
                                            <option value="5k-15k">$5k - $15k</option>
                                            <option value="15k-30k">$15k - $30k</option>
                                            <option value="30k+">$30k+</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block mb-2 font-medium text-gray-700">Do you have a vehicle to start with?</label>
                                        <div className="grid grid-cols-2 gap-4">
                                            <button
                                                type="button"
                                                className={`p-4 border rounded-lg transition-all ${vehicleOwnership === 'yes' ? 'bg-palatinate_blue text-white border-palatinate_blue' : 'text-gray-800'} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-palatinate_blue/10'}`}
                                                onClick={() => handleVehicleSelection('yes')}
                                                disabled={isSubmitting}
                                            >
                                                ✅ Yes
                                            </button>
                                            <button
                                                type="button"
                                                className={`p-4 border rounded-lg transition-all ${vehicleOwnership === 'no' ? 'bg-palatinate_blue text-white border-palatinate_blue' : 'text-gray-800'} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-palatinate_blue/10'}`}
                                                onClick={() => handleVehicleSelection('no')}
                                                disabled={isSubmitting}
                                            >
                                                ❌ No
                                            </button>
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block mb-2 font-medium text-gray-700">Target Launch Timeline</label>
                                        <select
                                            className="w-full p-3 border rounded-lg bg-white text-gray-800 focus:ring-2 focus:ring-pink-500 border-gray-300"
                                            value={formData.business.timeline}
                                            onChange={(e) => handleInputChange('business', 'timeline', e.target.value)}
                                            disabled={isSubmitting}
                                        >
                                            <option value="">Select timeline</option>
                                            <option value="1mo">Within 1 Month</option>
                                            <option value="3mo">1-3 Months</option>
                                            <option value="6mo">3-6 Months</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block mb-2 font-medium text-gray-700">Which Path Interests You?</label>
                                        <div className="grid grid-cols-2 gap-4">
                                            <button
                                                type="button"
                                                className={`p-4 border rounded-lg transition-all ${pathInterest === 'hustler' ? 'bg-pink-500 text-white border-pink-500' : 'text-gray-800'} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-pink-500/10'}`}
                                                onClick={() => handlePathSelection('hustler')}
                                                disabled={isSubmitting}
                                            >
                                                🚗 Hustler's Path
                                            </button>
                                            <button
                                                type="button"
                                                className={`p-4 border rounded-lg transition-all ${pathInterest === 'pro' ? 'bg-blue-600 text-white border-blue-600' : 'text-gray-800'} ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600/10'}`}
                                                onClick={() => handlePathSelection('pro')}
                                                disabled={isSubmitting}
                                            >
                                                🚐 Pro Path
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {currentStep === 3 && (
                            <div className="space-y-6">
                                <h3 className="text-xl font-semibold text-gray-800">Final Details</h3>
                                <div className="space-y-4">

                                    <div>
                                        <label className="block mb-2 font-medium text-gray-700">
                                            Where did you find us?
                                        </label>
                                        <input
                                            type="text"
                                            placeholder="e.g., Google, Instagram, Friend"
                                            className="w-full p-3 border rounded-lg text-gray-800 focus:ring-2 focus:ring-pink-500 border-gray-300"
                                            value={formData.motivation.findUs}
                                            onChange={e =>
                                                handleInputChange("motivation", "findUs", e.target.value)
                                            }
                                            required
                                            disabled={isSubmitting}
                                        />
                                    </div>
                                </div>
                            </div>
                        )}

                        <div className="flex justify-between">
                            {currentStep > 1 && (
                                <button
                                    type="button"
                                    onClick={() => setCurrentStep(currentStep - 1)}
                                    className={`px-6 py-2 text-palatinate_blue ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:text-blue-700'} font-medium`}
                                    disabled={isSubmitting}
                                >
                                    ← Back
                                </button>
                            )}
                            <button
                                type={currentStep === steps.length ? "submit" : "button"}
                                onClick={currentStep < steps.length ? () => setCurrentStep(currentStep + 1) : undefined}
                                className={`ml-auto px-6 py-3 bg-pink-500 text-white rounded-lg ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-pink-600'} transition-colors transform hover:scale-105 hover:shadow-lg hover:shadow-pink-500/30 font-bold`}
                                disabled={isSubmitting}
                            >
                                {isSubmitting && currentStep === steps.length ? "Submitting..." : currentStep === steps.length ? "Submit Application" : "Continue →"}
                            </button>
                        </div>
                        {currentStep === 3 && submitError && (
                            <div className="mt-4 text-red-500">{submitError}</div>
                        )}
                    </form>
                ) : (
                    <div className="p-6 text-center">
                        <h3 className="text-2xl font-bold text-gray-800 mb-4">Request Submitted!</h3>
                        <p className="text-gray-600 mb-6">Check your email for confirmation and next steps.</p>
                        <button
                            onClick={onClose}
                            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Close
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FranchiseApplicationPopup;
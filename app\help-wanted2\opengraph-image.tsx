import { ImageResponse } from "next/og";

export const alt = "Fleet Washing Jobs - Detail On The Go";
export const contentType = "image/png";
export const size = { width: 1200, height: 630 };

export default function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f7fafc",
          backgroundImage: "linear-gradient(135deg, #f7fafc 60%, #ffe7b3 100%)",
        }}
      >
        <div
          style={{
            fontSize: "48px",
            fontWeight: "bold",
            color: "#b36b00",
            marginBottom: "16px",
            letterSpacing: "-1px",
          }}
        >
          Fleet Washing Jobs
        </div>
        <div
          style={{
            fontSize: "32px",
            color: "#7a4c00",
            textAlign: "center",
            maxWidth: "80%",
            marginBottom: "16px",
          }}
        >
          $10 per vehicle • 17 minutes each
        </div>
        <div
          style={{
            fontSize: "24px",
            color: "#7a4c00",
            textAlign: "center",
            maxWidth: "80%",
            marginBottom: "24px",
          }}
        >
          Every other Sunday • Salina & Hays, KS
        </div>
        <div
          style={{
            fontSize: "22px",
            color: "#2d5c88",
            fontWeight: "500",
          }}
        >
          Detail On The Go
        </div>
      </div>
    ),
    size
  );
}
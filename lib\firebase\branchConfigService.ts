import { db } from './firebase';
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc,
  setDoc,
  query, 
  where, 
  orderBy,
  serverTimestamp,
  QueryDocumentSnapshot,
  DocumentData 
} from 'firebase/firestore';

// ===== DATABASE SCHEMA DESIGN =====
// 
// NEW COLLECTIONS (Safe - won't interfere with existing):
// - branchConfigs: Branch-specific pricing and service configurations  
// - vehiclePackages: Global package definitions
// - vehicleAddons: Global addon definitions
// - vehicleSizes: Global size definitions with multipliers
// - branchPricing: Branch-specific pricing overrides
//
// EXISTING COLLECTIONS (Untouched):
// - bookings: Customer bookings (keep as-is)
// - paymentLinks: Payment management (keep as-is) 
// - users: User authentication (keep as-is)

// ===== TYPE DEFINITIONS =====

export interface BranchConfig {
  id: string; // "lwr", "w-stl", "dvr", "ny"
  name: string;
  businessNumber: string;
  calendarId: string;
  collectionId: string;
  location: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  employeeInfo: {
    name: string;
    email: string;
    number: string;
  };
  reviewLink: string;
  branchLocation: string;
  timezone: string;
  operatingHours: {
    standard: { start: string; end: string };
    summer: { start: string; end: string };
    winter: { start: string; end: string };
  };
  serviceArea: {
    radius: number;
    cities: string[];
  };
  active: boolean;
  enabledVehicleTypes: string[];
  createdAt?: any;
  updatedAt?: any;
}

export interface VehiclePackage {
  id: string;
  name: string;
  vehicleType: 'car' | 'boat' | 'rv';
  basePrice: number; // Global base price
  description: string;
  images: string[];
  estimatedDuration: number; // hours
  requiresConsecutiveDays: boolean;
  weatherDependent: boolean;
  availableBranches: string[];
  active: boolean;
  sortOrder: number;
  createdAt?: any;
  updatedAt?: any;
}

export interface VehicleAddon {
  id: string;
  name: string;
  vehicleTypes: string[];
  basePrice: number; // Global base price
  hasQuantity: boolean;
  description: string;
  images: string[];
  estimatedDuration: number; // additional hours
  requiresSpecialTechnician: boolean;
  weatherDependent: boolean;
  availableBranches: string[];
  active: boolean;
  sortOrder: number;
  createdAt?: any;
  updatedAt?: any;
}

export interface VehicleSize {
  id: string;
  name: string;
  baseMultiplier: number; // Global base multiplier
  sortOrder: number;
  active: boolean;
  createdAt?: any;
  updatedAt?: any;
}

export interface BranchPricing {
  id: string; // branchId
  packagePricing: {
    [packageId: string]: {
      customPrice?: number; // Override global price
      available?: boolean; // Override availability
    };
  };
  addonPricing: {
    [addonId: string]: {
      customPrice?: number; // Override global price
      available?: boolean; // Override availability
    };
  };
  sizeMultipliers: {
    [sizeId: string]: {
      customMultiplier?: number; // Override global multiplier
    };
  };
  updatedAt?: any;
  updatedBy?: string; // Admin user ID
}

// ===== CACHE MANAGEMENT =====
const configCache = new Map<string, {
  data: any;
  timestamp: number;
  expiry: number;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

function getCachedData<T>(key: string): T | null {
  const cached = configCache.get(key);
  if (cached && Date.now() < cached.expiry) {
    return cached.data as T;
  }
  return null;
}

function setCachedData<T>(key: string, data: T): void {
  configCache.set(key, {
    data,
    timestamp: Date.now(),
    expiry: Date.now() + CACHE_DURATION
  });
}

// ===== FALLBACK DATA =====
// This matches your current hardcoded structure for seamless fallback
const fallbackData = {
  branches: {
    lwr: {
      id: 'lwr',
      name: 'Lawrence/KC',
      businessNumber: '+***********',
      calendarId: '<EMAIL>',
      collectionId: 'sms-lwr',
      location: 'Lawrence/KC and the surrounding areas',
      coordinates: { lat: 38.9717, lng: -95.2353 },
      employeeInfo: {
        name: 'Levi Taylor',
        email: '<EMAIL>',
        number: '+***********'
      },
      reviewLink: 'https://g.page/r/CS98X9jMS0IREBM/review',
      branchLocation: '197 Pinecone Dr, Lawrence, KS 66046',
      timezone: 'America/Chicago',
      operatingHours: {
        standard: { start: '8:00 AM', end: '6:00 PM' },
        summer: { start: '7:00 AM', end: '7:00 PM' },
        winter: { start: '9:00 AM', end: '5:00 PM' }
      },
      serviceArea: {
        radius: 30,
        cities: ['Lawrence', 'Kansas City', 'Topeka', 'Overland Park']
      },
      active: true,
      enabledVehicleTypes: ['car', 'boat', 'rv']
    }
    // Add other branches as needed
  },
  packages: {
    'interior-detailing': {
      id: 'interior-detailing',
      name: 'Interior Detailing',
      vehicleType: 'car' as const,
      basePrice: 249, // Updated price
      description: 'Complete interior cleaning and detailing',
      images: ['/interior detailing 1.png', '/interior detailing 2.png', '/interior detailing 3.jpg'],
      estimatedDuration: 3,
      requiresConsecutiveDays: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 1
    },
    'exterior-detailing': {
      id: 'exterior-detailing',
      name: 'Exterior Detailing',
      vehicleType: 'car' as const,
      basePrice: 99,
      description: 'Complete exterior wash and protection',
      images: ['/exterior wash 1.jpg', '/exterior wash 2.jpg', '/exterior wash 3.jpg'],
      estimatedDuration: 2,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 2
    },
    'interior-exterior-detailing': {
      id: 'interior-exterior-detailing',
      name: 'Interior & Exterior Detailing',
      vehicleType: 'car' as const,
      basePrice: 260,
      description: 'Complete interior and exterior detailing package',
      images: ['/exterior wash 5.png', '/interior detailing 3.jpg'],
      estimatedDuration: 4.5,
      requiresConsecutiveDays: false,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 3
    }
  },
  sizes: {
    'sedan': {
      id: 'sedan',
      name: 'Sedan',
      baseMultiplier: 1.0,
      sortOrder: 1,
      active: true
    },
    'small-suv': {
      id: 'small-suv',
      name: 'Small SUV (2 Rows)',
      baseMultiplier: 1.1,
      sortOrder: 2,
      active: true
    },
    'large-suv': {
      id: 'large-suv',
      name: 'Large-SUV (3 Rows)',
      baseMultiplier: 1.15,
      sortOrder: 3,
      active: true
    },
    'small-truck': {
      id: 'small-truck',
      name: 'Small/Mid-Truck',
      baseMultiplier: 1.05,
      sortOrder: 4,
      active: true
    },
    'large-truck': {
      id: 'large-truck',
      name: 'Large-Truck',
      baseMultiplier: 1.1,
      sortOrder: 5,
      active: true
    },
    'van': {
      id: 'van',
      name: 'Van (4+ Rows)',
      baseMultiplier: 1.7,
      sortOrder: 6,
      active: true
    }
  },
  addons: {
    'window-shield-ceramic-coating': {
      id: 'window-shield-ceramic-coating',
      name: 'Window Shield Ceramic Coating',
      vehicleTypes: ['car'],
      basePrice: 25,
      hasQuantity: false,
      description: 'Get crystal-clear visibility in any weather. Our advanced coating repels water, dirt, and debris for safer, easier driving.',
      images: ['/windowceramic5.png', '/windowceramic4.png', '/windowceramic3.jpg', '/windowceramic2.jpg', '/windowceramic1.jpeg'],
      estimatedDuration: 0.5,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 1
    },
    'engine-clean': {
      id: 'engine-clean',
      name: 'Engine Clean',
      vehicleTypes: ['car'],
      basePrice: 50,
      hasQuantity: false,
      description: 'Boost your engine\'s performance and longevity. Professional cleaning removes built-up grime and prevents potential issues.',
      images: ['/engine clean 1.JPG'],
      estimatedDuration: 1,
      requiresSpecialTechnician: false,
      weatherDependent: false,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 2
    },
    'full-body-ceramic-coating': {
      id: 'full-body-ceramic-coating',
      name: 'Full Body Ceramic Coating',
      vehicleTypes: ['car'],
      basePrice: 600,
      hasQuantity: false,
      description: 'Ultimate paint protection. Achieve a showroom shine while defending against UV rays, chemicals, and minor scratches.',
      images: ['/ceramic1.jpg', '/ceramic2.jpg', '/ceramic3.jpg', '/ceramic4.png'],
      estimatedDuration: 8,
      requiresSpecialTechnician: true,
      weatherDependent: true,
      availableBranches: ['lwr', 'w-stl', 'dvr', 'ny'],
      active: true,
      sortOrder: 3
    }
  }
};

// ===== DATABASE SERVICE FUNCTIONS =====

/**
 * Get branch configuration (with fallback to hardcoded data)
 */
export async function getBranchConfig(branchId: string): Promise<BranchConfig | null> {
  const cacheKey = `branchConfig_${branchId}`;
  const cached = getCachedData<BranchConfig>(cacheKey);
  if (cached) return cached;

  try {
    const branchDoc = await getDoc(doc(db, 'branchConfigs', branchId));
    if (branchDoc.exists()) {
      const data = { id: branchDoc.id, ...branchDoc.data() } as BranchConfig;
      setCachedData(cacheKey, data);
      return data;
    }
  } catch (error) {
    console.error(`Error fetching branch config ${branchId}:`, error);
  }
  
  // Fallback to hardcoded data
  const fallback = fallbackData.branches[branchId as keyof typeof fallbackData.branches];
  return fallback || null;
}

/**
 * Get packages for a specific vehicle type and branch (with pricing overrides)
 */
export async function getBranchPackages(branchId: string, vehicleType: string): Promise<VehiclePackage[]> {
  const cacheKey = `packages_${branchId}_${vehicleType}`;
  const cached = getCachedData<VehiclePackage[]>(cacheKey);
  if (cached) return cached;

  try {
    // Get global packages
    const packagesQuery = query(
      collection(db, 'vehiclePackages'),
      where('vehicleType', '==', vehicleType),
      where('availableBranches', 'array-contains', branchId),
      where('active', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const packagesSnapshot = await getDocs(packagesQuery);
    let packages = packagesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as VehiclePackage[];

    // Get branch-specific pricing overrides
    const branchPricingDoc = await getDoc(doc(db, 'branchPricing', branchId));
    if (branchPricingDoc.exists()) {
      const branchPricing = branchPricingDoc.data() as BranchPricing;
      
      // Apply pricing overrides
      packages = packages.map(pkg => {
        const override = branchPricing.packagePricing?.[pkg.id];
        if (override) {
          return {
            ...pkg,
            basePrice: override.customPrice ?? pkg.basePrice,
            active: override.available ?? pkg.active
          };
        }
        return pkg;
      }).filter(pkg => pkg.active);
    }
    
    setCachedData(cacheKey, packages);
    return packages;
    
  } catch (error) {
    console.error(`Error fetching packages for branch ${branchId}, vehicle ${vehicleType}:`, error);
    
    // Fallback to hardcoded data
    return Object.values(fallbackData.packages)
      .filter(pkg => pkg.vehicleType === vehicleType && pkg.availableBranches.includes(branchId));
  }
}

/**
 * Get vehicle sizes with branch-specific multipliers
 */
export async function getBranchVehicleSizes(branchId: string): Promise<VehicleSize[]> {
  const cacheKey = `vehicleSizes_${branchId}`;
  const cached = getCachedData<VehicleSize[]>(cacheKey);
  if (cached) return cached;

  try {
    // Get global sizes
    const sizesQuery = query(
      collection(db, 'vehicleSizes'),
      where('active', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const sizesSnapshot = await getDocs(sizesQuery);
    let sizes = sizesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as VehicleSize[];

    // Get branch-specific multiplier overrides
    const branchPricingDoc = await getDoc(doc(db, 'branchPricing', branchId));
    if (branchPricingDoc.exists()) {
      const branchPricing = branchPricingDoc.data() as BranchPricing;
      
      // Apply multiplier overrides
      sizes = sizes.map(size => {
        const override = branchPricing.sizeMultipliers?.[size.id];
        if (override?.customMultiplier !== undefined) {
          return {
            ...size,
            baseMultiplier: override.customMultiplier
          };
        }
        return size;
      });
    }
    
    setCachedData(cacheKey, sizes);
    return sizes;
    
  } catch (error) {
    console.error(`Error fetching vehicle sizes for branch ${branchId}:`, error);
    
    // Fallback to hardcoded data
    return Object.values(fallbackData.sizes);
  }
}

/**
 * Get addons for a specific vehicle type and branch (with pricing overrides)
 */
export async function getBranchAddons(branchId: string, vehicleType: string): Promise<VehicleAddon[]> {
  const cacheKey = `addons_${branchId}_${vehicleType}`;
  const cached = getCachedData<VehicleAddon[]>(cacheKey);
  if (cached) return cached;

  try {
    // Get global addons for this vehicle type
    const addonsQuery = query(
      collection(db, 'vehicleAddons'),
      where('vehicleTypes', 'array-contains', vehicleType),
      where('active', '==', true),
      orderBy('sortOrder', 'asc')
    );
    
    const addonsSnapshot = await getDocs(addonsQuery);
    const addons = addonsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as VehicleAddon));

    // Get branch-specific pricing overrides
    const branchPricingDoc = await getDoc(doc(db, 'branchPricing', branchId));
    const branchPricing = branchPricingDoc.exists() ? branchPricingDoc.data() : null;

    // Apply branch-specific pricing and availability
    const branchAddons = addons
      .filter(addon => addon.availableBranches.includes(branchId))
      .map(addon => {
        const override = branchPricing?.addonPricing?.[addon.id];
        if (override) {
          return {
            ...addon,
            basePrice: override.customPrice !== undefined ? override.customPrice : addon.basePrice,
          };
        }
        return addon;
      });
    
    setCachedData(cacheKey, branchAddons);
    return branchAddons;
    
  } catch (error) {
    console.error(`Error fetching addons for branch ${branchId} and vehicle type ${vehicleType}:`, error);
    
    // Fallback to hardcoded data based on vehicle type
    if (vehicleType === 'car') {
      return Object.values(fallbackData.addons);
    }
    return []; // No addons for boat/RV in fallback data
  }
}

/**
 * Get complete vehicle configuration for a branch (combines all data)
 */
export async function getCompleteBranchConfig(branchId: string) {
  try {
    const [branchConfig, carPackages, vehicleSizes] = await Promise.all([
      getBranchConfig(branchId),
      getBranchPackages(branchId, 'car'),
      getBranchVehicleSizes(branchId)
    ]);

    return {
      branch: branchConfig,
      car: {
        packages: carPackages,
        sizes: vehicleSizes
      }
    };
  } catch (error) {
    console.error(`Error fetching complete config for branch ${branchId}:`, error);
    
    // Return fallback structure
    return {
      branch: fallbackData.branches[branchId as keyof typeof fallbackData.branches] || null,
      car: {
        packages: Object.values(fallbackData.packages),
        sizes: Object.values(fallbackData.sizes)
      }
    };
  }
}

// ===== ADMIN FUNCTIONS (For the admin dashboard) =====

/**
 * Update branch-specific pricing (for admin use)
 */
export async function updateBranchPricing(
  branchId: string, 
  updates: Partial<BranchPricing>,
  adminUserId: string
): Promise<boolean> {
  try {
    const branchPricingRef = doc(db, 'branchPricing', branchId);
    
    await setDoc(branchPricingRef, {
      id: branchId,
      ...updates,
      updatedAt: serverTimestamp(),
      updatedBy: adminUserId
    }, { merge: true });

    // Clear cache for this branch
    clearBranchCache(branchId);
    
    return true;
  } catch (error) {
    console.error(`Error updating branch pricing for ${branchId}:`, error);
    return false;
  }
}

/**
 * Create/update a vehicle package (for admin use)
 */
export async function upsertVehiclePackage(
  packageData: Omit<VehiclePackage, 'createdAt' | 'updatedAt'>,
  adminUserId: string
): Promise<boolean> {
  try {
    const packageRef = packageData.id 
      ? doc(db, 'vehiclePackages', packageData.id)
      : doc(collection(db, 'vehiclePackages'));
    
    await setDoc(packageRef, {
      ...packageData,
      updatedAt: serverTimestamp(),
      ...(packageData.id ? {} : { createdAt: serverTimestamp() })
    }, { merge: true });

    // Clear related caches
    packageData.availableBranches.forEach(branchId => {
      clearBranchCache(branchId);
    });
    
    return true;
  } catch (error) {
    console.error('Error upserting vehicle package:', error);
    return false;
  }
}

/**
 * Clear cache for a specific branch or all cache
 */
export function clearBranchCache(branchId?: string): void {
  if (branchId) {
    const keysToDelete = Array.from(configCache.keys()).filter(key => 
      key.includes(branchId)
    );
    keysToDelete.forEach(key => configCache.delete(key));
  } else {
    configCache.clear();
  }
}

/**
 * Migration helper: Initialize branch with current hardcoded data
 */
export async function initializeBranchData(branchId: string): Promise<boolean> {
  try {
    // Check if branch config already exists
    const branchConfigDoc = await getDoc(doc(db, 'branchConfigs', branchId));
    if (branchConfigDoc.exists()) {
      console.log(`Branch ${branchId} already initialized`);
      return true;
    }

    // Initialize with fallback data
    const branchData = fallbackData.branches[branchId as keyof typeof fallbackData.branches];
    if (!branchData) {
      console.error(`No fallback data for branch ${branchId}`);
      return false;
    }

    // Create branch config
    await setDoc(doc(db, 'branchConfigs', branchId), {
      ...branchData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    console.log(`Successfully initialized branch ${branchId}`);
    return true;
    
  } catch (error) {
    console.error(`Error initializing branch ${branchId}:`, error);
    return false;
  }
}

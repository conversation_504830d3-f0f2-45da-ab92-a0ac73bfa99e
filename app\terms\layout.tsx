import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms & Conditions | Detail On The Go",
  description: "Read our terms and conditions for Detail On The Go mobile detailing services. Learn about our policies, guarantees, and service agreements.",
  openGraph: {
    title: "Terms & Conditions | Detail On The Go",
    description: "Read our terms and conditions for Detail On The Go mobile detailing services. Learn about our policies, guarantees, and service agreements.",
    url: "https://detailongo.com/terms",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

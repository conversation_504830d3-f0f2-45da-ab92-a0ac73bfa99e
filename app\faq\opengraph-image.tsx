import { ImageResponse } from "next/og";

export const alt = "FAQ - Detail On The Go";
export const contentType = "image/png";
export const size = { width: 1200, height: 630 };

export default function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f7fafc",
          backgroundImage: "linear-gradient(135deg, #f7fafc 60%, #d1eaff 100%)",
        }}
      >
        <div
          style={{
            fontSize: "54px",
            fontWeight: "bold",
            color: "#003366",
            marginBottom: "24px",
            letterSpacing: "-1px",
          }}
        >
          Frequently Asked Questions
        </div>
        <div
          style={{
            fontSize: "28px",
            color: "#1a3a5d",
            textAlign: "center",
            maxWidth: "80%",
          }}
        >
          Answers about our mobile detailing services
        </div>
        <div
          style={{
            fontSize: "22px",
            color: "#2d5c88",
            marginTop: "32px",
            fontWeight: "500",
          }}
        >
          Detail On The Go
        </div>
      </div>
    ),
    size
  );
}

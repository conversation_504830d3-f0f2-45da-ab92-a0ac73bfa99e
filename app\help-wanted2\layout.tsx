import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Fleet Washing Jobs | Part-Time Work | Detail On The Go',
    description: 'Easy part-time fleet washing jobs available in Kansas. $10 per vehicle, 17 minutes each, every other Sunday. Apply for positions in Salina, Hays, and Lawrence.',
    keywords: [
        'fleet washing jobs',
        'part-time work',
        'car washing jobs',
        'Kansas jobs',
        'Salina jobs',
        'Hays jobs',
        '<PERSON> jobs',
        'Detail On The Go jobs',
        'weekend work',
        'flexible schedule'
    ],
    alternates: {
        canonical: 'https://www.detailongo.com/help-wanted2/',
    },
    openGraph: {
        title: 'Fleet Washing Jobs | Part-Time Work | Detail On The Go',
        description: 'Easy part-time fleet washing jobs. $10 per vehicle, work every other Sunday. Positions available in Salina, Hays, and Lawrence, KS.',
        url: 'https://detailongo.com/help-wanted2/',
        images: [
            {
                url: '/images/fleet-washing-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Fleet Washing Jobs - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Fleet Washing Jobs | Part-Time Work | Detail On The Go',
        description: 'Easy part-time fleet washing jobs. $10 per vehicle, work every other Sunday. Apply today!',
        images: ['/images/fleet-washing-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "JobPosting",
                        "title": "Fleet Washing Specialist",
                        "description": "Part-time fleet washing position. Wash 7 vehicles every other Sunday, $10 per vehicle. Each vehicle takes approximately 17 minutes to wash and dry.",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "logo": "https://detailongo.com/images/logo.png",
                            "telephone": "******-615-6156"
                        },
                        "jobLocation": [
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Salina",
                                    "addressRegion": "KS",
                                    "addressCountry": "USA"
                                }
                            },
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Hays",
                                    "addressRegion": "KS",
                                    "addressCountry": "USA"
                                }
                            },
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Lawrence",
                                    "addressRegion": "KS",
                                    "addressCountry": "USA"
                                }
                            }
                        ],
                        "employmentType": "PART_TIME",
                        "workHours": "4 hours per month",
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "USD",
                            "value": {
                                "@type": "QuantitativeValue",
                                "value": 140,
                                "unitText": "MONTH"
                            }
                        },
                        "industry": "Automotive Services",
                        "url": "https://detailongo.com/help-wanted2/",
                        "datePosted": new Date().toISOString(),
                        "validThrough": new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() // 90 days from now
                    })
                }}
            />
            {children}
        </>
    );
}
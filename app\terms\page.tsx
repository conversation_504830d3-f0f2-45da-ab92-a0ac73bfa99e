"use client";

import { helvetica } from "../fonts";
import cx from "classnames";
import Link from "next/link";

export default function TermsAndConditions() {
    return (
        <div
            className={cx(
                helvetica.variable,
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl border border-white/30 text-white font-sans mx-4"
            )}
        >
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-12">
                    <h1 className="text-4xl md:text-5xl font-black tracking-tighter mb-4 text-white">
                        Terms & Conditions
                    </h1>
                    <p className="text-lg text-gray-200">
                        Please read these terms and conditions carefully before using our services.
                    </p>
                    <p className="text-sm text-gray-300 mt-2">
                        Last updated: September 18, 2025
                    </p>
                </div>

                <div className="prose prose-lg max-w-none">
                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-white mb-4">Acceptance of Terms</h2>
                        <div className="space-y-4 text-gray-200">
                            <p>
                                By accessing and using Detail On The Go's services, you accept and agree to be bound by the
                                terms and provision of this agreement. If you do not agree to abide by the above, please do
                                not use this service.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-white mb-4">Service Description</h2>
                        <div className="space-y-4 text-gray-200">
                            <p>
                                Detail On The Go provides mobile automotive, boat, and RV detailing services. Our services include:
                            </p>
                            <ul className="list-disc pl-6 space-y-2">
                                <li>Interior and exterior vehicle cleaning</li>
                                <li>Ceramic coating applications</li>
                                <li>Paint correction and polishing</li>
                                <li>Boat and RV detailing services</li>
                                <li>Fleet washing services</li>
                                <li>Additional automotive care services as offered</li>
                            </ul>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-white mb-4">Booking and Payment</h2>
                        <div className="space-y-4 text-gray-200">
                            <p>
                                <strong>Booking:</strong> All services must be booked in advance through our website, phone, or approved booking platforms.
                            </p>
                            <p>
                                <strong>Payment:</strong> Payment is due at the time of service completion unless other arrangements have been made. 
                                We accept cash, credit cards, and digital payments.
                            </p>
                            <p>
                                <strong>Pricing:</strong> All prices are subject to change without notice. Final pricing will be confirmed at the time of booking.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Cancellation Policy</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                <strong>Customer Cancellations:</strong> Customers may cancel or reschedule appointments with at least 
                                24 hours notice without penalty. Cancellations with less than 24 hours notice may be subject to a cancellation fee.
                            </p>
                            <p>
                                <strong>Weather Cancellations:</strong> Services may be cancelled or rescheduled due to inclement weather 
                                conditions at our discretion for safety reasons.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Liability and Insurance</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                Detail On The Go carries comprehensive liability insurance. However, our liability is limited to the cost 
                                of the service provided. We are not responsible for:
                            </p>
                            <ul className="list-disc pl-6 space-y-2">
                                <li>Pre-existing damage to vehicles</li>
                                <li>Items left in vehicles during service</li>
                                <li>Damage caused by normal wear and tear</li>
                                <li>Damage to vehicles with compromised paint or clear coat</li>
                                <li>Electronic malfunctions unrelated to our services</li>
                            </ul>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Customer Responsibilities</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>Customers are responsible for:</p>
                            <ul className="list-disc pl-6 space-y-2">
                                <li>Providing accurate vehicle information and service location</li>
                                <li>Removing personal items from vehicles before service</li>
                                <li>Ensuring vehicle access and adequate workspace</li>
                                <li>Providing access to water and electricity when required</li>
                                <li>Informing us of any pre-existing damage or concerns</li>
                            </ul>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Service Guarantee</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                We stand behind our work and offer a satisfaction guarantee. If you are not satisfied with our service, 
                                please contact us within 48 hours of service completion. We will work with you to address any concerns 
                                and may provide additional service at no charge if warranted.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Intellectual Property</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                All content on our website, including text, graphics, logos, and images, is the property of 
                                Detail On The Go, LLC and is protected by copyright and trademark laws.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Dispute Resolution</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                Any disputes arising from these terms or our services will be resolved through binding arbitration 
                                in accordance with the laws of the state where the service was provided.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Changes to Terms</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                We reserve the right to modify these terms at any time. Changes will be effective immediately 
                                upon posting on our website. Continued use of our services constitutes acceptance of modified terms.
                            </p>
                        </div>
                    </section>

                    <section className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Information</h2>
                        <div className="space-y-4 text-gray-700">
                            <p>
                                If you have any questions about these terms and conditions, please contact us:
                            </p>
                            <div className="bg-gray-50 p-6 rounded-lg">
                                <p><strong>Detail On The Go, LLC</strong></p>
                                <p>Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
                                <p>Phone: <a href="tel:+17856156156" className="text-blue-600 hover:underline">(*************</a></p>
                                <p>
                                    <Link href="/contact" className="text-blue-600 hover:underline">
                                        Contact Form
                                    </Link>
                                </p>
                            </div>
                        </div>
                    </section>
                </div>

                <div className="text-center mt-12">
                    <Link
                        href="/"
                        className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        ← Back to Home
                    </Link>
                </div>
            </div>
        </div>
    );
}

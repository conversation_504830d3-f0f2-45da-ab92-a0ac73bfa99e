import { Metadata } from "next";

export const metadata: Metadata = {
  title: "RV & Boat Detailing Services | Detail On The Go",
  description: "Professional mobile RV and boat detailing services. We come to you with specialized equipment for comprehensive cleaning, polishing, and protection of your recreational vehicles.",
  keywords: "RV detailing, boat detailing, mobile RV cleaning, boat washing, RV ceramic coating, boat polishing, recreational vehicle detailing",
  openGraph: {
    title: "RV & Boat Detailing Services | Detail On The Go",
    description: "Professional mobile RV and boat detailing services. We come to you with specialized equipment for comprehensive cleaning, polishing, and protection of your recreational vehicles.",
    url: "https://detailongo.com/rv-boat",
    type: "website",
    images: [
      {
        url: "/rv1.jpg",
        width: 1200,
        height: 630,
        alt: "Professional RV detailing services",
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

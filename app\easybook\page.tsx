"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import ShinyText from "@/components/ShinyText";
import BookingSoftwareSignupPopup from "@/components/bookingSystemSignUp";

const ImageCarousel = ({ images, className = "" }: { images: string[]; className?: string }) => {
    const [activeIndex, setActiveIndex] = useState(0);

    const nextSlide = () => {
        setActiveIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    };

    const prevSlide = () => {
        setActiveIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    };



    // Auto-advance effect
    useEffect(() => {
        const interval = setInterval(() => {
            nextSlide();
        }, 5000); // 5 seconds

        return () => clearInterval(interval);
    }, [activeIndex]); // Reset timer when activeIndex changes

    return (
        <div className={`w-full mt-8 max-w-4xl mx-auto ${className}`}>
            <div className="relative aspect-video rounded-lg shadow-xl border-4 border-white overflow-hidden">
                <Image
                    src={images[activeIndex]}
                    alt="Feature demonstration"
                    fill
                    className="object-cover transition-opacity duration-500 opacity-0"
                    onLoadingComplete={(img) => img.classList.remove("opacity-0")}
                    priority
                />
            </div>

            {/* Navigation Dots and Arrows */}
            <div className="mt-4 flex justify-center items-center space-x-4">
                {/* Prev Button */}
                <button
                    onClick={prevSlide}
                    className="bg-white text-black text-lg px-3 py-1 rounded-full shadow focus:outline-none hover:bg-gray-100 transition-colors"
                >
                    👈
                </button>

                {/* Dots */}
                <div className="flex space-x-2">
                    {images.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => setActiveIndex(index)}
                            className={`w-3 h-3 rounded-full transition-colors ${index === activeIndex ? 'bg-pink-500' : 'bg-white/50 hover:bg-white/70'}`}
                        />
                    ))}
                </div>

                {/* Next Button */}
                <button
                    onClick={nextSlide}
                    className="bg-white text-black text-lg px-3 py-1 rounded-full shadow focus:outline-none hover:bg-gray-100 transition-colors"
                >
                    👉
                </button>
            </div>
        </div>
    );
};

const DetailerBookingPage = () => {
    const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);
    const [showSignupPopup, setShowSignupPopup] = useState(false);
    const imageGroups = {
        bookingProcess: [

            '/EasyBook 1(Customer First Slide).png',
            '/EasyBook 1(Customer Selecting Vehicle & Packages).png',
            '/EasyBook (Customer Selecting Date).png',
            '/EasyBook 2(Customer Selecting Time & Frequency).png',
            '/EasyBook 3(Customer Filling in Their Information).png',
            '/EasyBook 4(Customer Filling In Billing).png',
            '/EasyBook 5(Customer on Confirmation Page).png',
            '/EasyBook (Customer Email Confirmation).png',
            '/EasyBook (Customer Text).png',
        ],
        management: [
            '/EasyBook 6(Detailer Calendar View).png',
            '/EasyBook 7(Detailer Client Event View).png',
            '/EasyBook 9(Detailer Collecting Payment and View Payments).png'
        ],
        communication: [
            '/EasyBook 8(Detailer Communication SMS).png',
            '/EasyBook (Detailer Booking Text).png',
            '/EasyBook 10(Contact List).png',
            '/EasyBook 10(Shortcut Messages).png'
        ]
    };


    const faqItems = [
        {
            question: "How much does it cost to use the booking system?",
            answer: "Our system is affordably priced at just $10 per month, with no hidden fees.",
        },
        {
            question: "Do I need any technical skills to set it up?",
            answer: "No, our platform is designed to be user-friendly. We provide step-by-step guides and support to help you get started.",
        },
        {
            question: "Can I customize the look and feel to match my brand?",
            answer: "Absolutely! You can adjust colors, fonts, and add your logo to make the booking system seamlessly integrate with your brand.",
        },
        {
            question: "How does the payment processing work?",
            answer: "You can choose to collect payments online via Stripe (we'll help you with the setup) or handle them separately.",
        },
        {
            question: "What kind of support do you offer?",
            answer: "We provide email and chat support, as well as a comprehensive knowledge base to answer your questions.",
        },
    ];

    return (
        <main className="min-h-screen bg-transparent w-full p-0">
            {/* Hero Section */}
            <section className="relative py-12 md:pt-16 md:pb-8">
                <div className="relative max-w-6xl mx-auto px-4 flex flex-col md:flex-row items-center gap-8">
                    {/* Left Column - Text Content */}
                    <div className="md:w-1/2 text-center md:text-left">
                        <h1 className="mb-4">
                            <ShinyText
                                text="5X Your Revenue"
                                speed={5}
                                className="text-5xl md:text-6xl font-display font-bold italic text-white tracking-tight"
                            />
                        </h1>
                        <p className="text-xl text-white/90 mb-8">
                            Streamline your operations, boost conversions, and grow your business with our customizable, mobile-friendly booking platform designed specifically for solo detailers.
                        </p>
                        <button
                            onClick={() => setShowSignupPopup(true)}
                            className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                        >
                            <span className="relative z-10 italic font-bold text-xl">Try Free</span>
                            <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                        </button>
                    </div>

                    {/* Right Column - Image Carousel */}
                    <div className="md:w-1/2">
                        <ImageCarousel
                            images={imageGroups.bookingProcess}
                            className="max-w-md mx-auto"
                        />
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <div className="bg-pink-600/90 backdrop-blur-xl">
                <section id="features" className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12 text-white">
                            Key Features
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {[
                                {
                                    icon: "📱",
                                    title: "Seamless Booking Experience",
                                    description: "Guide customers through a mobile-friendly, multistep process: location, services, scheduling, and more.",
                                    images: imageGroups.bookingProcess
                                },
                                {
                                    icon: "💳",
                                    title: "Dynamic Pricing & Payments",
                                    description: "Set custom rates with live recalculation, integrated taxes, and flexible Stripe or on-site payment options.",
                                    images: imageGroups.management
                                },
                                {
                                    icon: "📞",
                                    title: "Centralized Messaging",
                                    description: "Manage SMS and email from one number, keeping team and client communication in sync.",
                                    images: imageGroups.communication
                                },
                                {
                                    icon: "🎨",
                                    title: "Custom Branding",
                                    description: "Match your brand with colors, fonts, logos, and personalized emails sent from your address."
                                },
                                {
                                    icon: "📈",
                                    title: "Marketing Automation",
                                    description: "Recover abandoned carts, retarget visitors with ads, and suggest upsells automatically."
                                },
                                {
                                    icon: "💻",
                                    title: "Developer-Friendly",
                                    description: "Built with React and TypeScript, offering modular components and easy Tailwind theming."
                                },
                            ].map((feature, idx) => (
                                <div key={idx} className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                                    <div className="text-5xl mb-4 text-white">{feature.icon}</div>
                                    <h3 className="text-xl font-semibold mb-2 text-white">{feature.title}</h3>
                                    <p className="text-gray-200 mb-4">{feature.description}</p>
                                    {feature.images && <ImageCarousel images={feature.images} />}
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
            </div>


            {/* Benefits Section */}
            <div className="bg-palatinate_blue/90 backdrop-blur-xl">
                <section className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12 text-white">
                            Why Solo Detailers Love It
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {[
                                {
                                    icon: "🎛️",
                                    title: "Total Control",
                                    description: "Price every service your way and manage your business on your terms."
                                },
                                {
                                    icon: "🛠️",
                                    title: "Turnkey Booking",
                                    description: "One widget handles calendar, payments, marketing, and messaging."
                                },
                                {
                                    icon: "📈",
                                    title: "Higher Conversions",
                                    description: "Intuitive flow, abandoned-cart recovery, and retargeting boost bookings."
                                },
                                {
                                    icon: "✨",
                                    title: "Professional Polish",
                                    description: "Branded emails and upsells enhance your business image."
                                },
                                {
                                    icon: "📊",
                                    title: "Data & Insights",
                                    description: "Track lead sources, drop-offs, and sign-ups to optimize growth."
                                },
                                {
                                    icon: "💰",
                                    title: "Affordable",
                                    description: "Just $10/month for a fully featured, customizable system."
                                },
                            ].map((benefit, idx) => (
                                <div key={idx} className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                                    <div className="text-5xl mb-4 text-white">{benefit.icon}</div>
                                    <h3 className="text-xl font-semibold mb-2 text-white">{benefit.title}</h3>
                                    <p className="text-gray-200">{benefit.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
            </div>

            {/* How It Works Section */}
            <div className="bg-blue-600/90 backdrop-blur-xl">
                <section className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12 text-white">
                            How It Works
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div className="space-y-12">
                                {[
                                    {
                                        step: "1",
                                        title: "Set Up Your Profile",
                                        description: "Add your location, contact info, and branding with address autocomplete."
                                    },
                                    {
                                        step: "2",
                                        title: "Configure Services",
                                        description: "Define custom services and prices for cars, boats, RVs, and add-ons."
                                    },
                                    {
                                        step: "3",
                                        title: "Embed on Your Site",
                                        description: "Add the booking widget to your website with a simple code snippet."
                                    },
                                ].map((step, idx) => (
                                    <div key={idx} className="flex flex-col">
                                        <div className="bg-white text-blue-600 w-12 h-12 rounded-full flex items-center justify-center text-2xl font-bold mb-4">
                                            {step.step}
                                        </div>
                                        <h3 className="text-xl font-semibold text-white mb-2">{step.title}</h3>
                                        <p className="text-gray-200">{step.description}</p>
                                    </div>
                                ))}
                            </div>
                            <div className="space-y-12">
                                {[
                                    {
                                        step: "4",
                                        title: "Manage Bookings",
                                        description: "Handle appointments, payments, and client notes effortlessly.",
                                        images: imageGroups.management
                                    },
                                    {
                                        step: "5",
                                        title: "Grow Your Business",
                                        description: "Use analytics and marketing tools to expand your reach."
                                    }
                                ].map((step, idx) => (
                                    <div key={idx} className="flex flex-col">
                                        <div className="bg-white text-blue-600 w-12 h-12 rounded-full flex items-center justify-center text-2xl font-bold mb-4">
                                            {step.step}
                                        </div>
                                        <h3 className="text-xl font-semibold text-white mb-2">{step.title}</h3>
                                        <p className="text-gray-200 mb-4">{step.description}</p>
                                        {step.images && <ImageCarousel images={step.images} />}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            <div className="bg-purple-600/90 backdrop-blur-xl">
                <section className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12 text-white">
                            Powerful Analytics to Grow Your Business
                        </h2>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                            <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                                <h3 className="text-xl font-semibold mb-4 text-white">📊 Customer Behavior Insights</h3>
                                <ul className="space-y-3 text-gray-200">
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>See which services get the most views and bookings</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>Track where customers drop off in booking process</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>Identify popular add-ons and upsell opportunities</span>
                                    </li>
                                </ul>
                            </div>

                            <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                                <h3 className="text-xl font-semibold mb-4 text-white">📱 Device & Platform Data</h3>
                                <ul className="space-y-3 text-gray-200">
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>See what devices customers use to book (75% mobile)</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>Identify peak booking times and days</span>
                                    </li>
                                    <li className="flex items-start">
                                        <span className="mr-2">•</span>
                                        <span>Track which marketing sources drive most bookings</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div className="bg-white/10 backdrop-blur-lg p-8 rounded-xl border border-white/20">
                            <h3 className="text-2xl font-bold text-center mb-6 text-white">Real Performance Results</h3>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div className="p-4">
                                    <div className="text-4xl font-bold text-pink-400 mb-2">+58%</div>
                                    <div className="text-white">Mobile Conversions</div>
                                </div>
                                <div className="p-4">
                                    <div className="text-4xl font-bold text-pink-400 mb-2">-40%</div>
                                    <div className="text-white">Booking Abandonment</div>
                                </div>
                                <div className="p-4">
                                    <div className="text-4xl font-bold text-pink-400 mb-2">+35%</div>
                                    <div className="text-white">Average Order Value</div>
                                </div>
                                <div className="p-4">
                                    <div className="text-4xl font-bold text-pink-400 mb-2">22%</div>
                                    <div className="text-white">Repeat Customers</div>
                                </div>
                            </div>
                            <p className="text-center text-gray-300 mt-6">
                                Actual results from detailers using our booking analytics
                            </p>
                        </div>

                        <div className="mt-12 text-center">
                            <button
                                onClick={() => setShowSignupPopup(true)}
                                className="group relative bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105"
                            >
                                <span className="relative z-10 font-bold text-lg">Unlock Your Business Insights</span>
                                <span className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </button>
                        </div>
                    </div>
                </section>
            </div>
            {/* FAQ Section */}
            <div className="bg-palatinate_blue-700/20 backdrop-blur-xl">
                <section className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12 text-white">
                            Frequently Asked Questions
                        </h2>
                        <div className="space-y-4">
                            {faqItems.map((item, index) => (
                                <div key={index} className="bg-palatinate_blue-800 rounded-lg border border-palatinate_blue-600">
                                    <button
                                        onClick={() => setOpenFaqIndex(openFaqIndex === index ? null : index)}
                                        className="w-full p-6 text-left flex justify-between items-center hover:bg-palatinate_blue-700 transition-colors"
                                    >
                                        <h3 className="text-xl font-semibold text-white">{item.question}</h3>
                                        <span className="text-2xl text-white">{openFaqIndex === index ? "−" : "+"}</span>
                                    </button>
                                    {openFaqIndex === index && (
                                        <div className="p-6 pt-0 border-t border-palatinate_blue-600">
                                            <p className="text-gray-200 leading-relaxed">{item.answer}</p>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </section>
            </div>

            {/* Call to Action Section */}
            <div className="bg-white/70 backdrop-blur-lg">
                <section className="relative py-16">
                    <div className="max-w-6xl mx-auto px-4 text-center">
                        <h2 className="text-3xl font-bold text-gray-800 mb-6">
                            Ready to Get Started?
                        </h2>
                        <p className="text-xl text-gray-700 mb-8">
                            Sign up today and take your detailing business to the next level.
                        </p>
                        <button
                            onClick={() => setShowSignupPopup(true)}
                            className="group relative bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-blue-500 shiny-button border border-white"
                        >
                            <span className="relative z-10 italic font-bold text-xl">Try For Free</span>
                            <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                        </button>
                    </div>
                </section>
            </div>
            {showSignupPopup && (
                <BookingSoftwareSignupPopup onClose={() => setShowSignupPopup(false)} />
            )}
        </main>
    );
};

export default DetailerBookingPage;

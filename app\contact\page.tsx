"use client";

import cx from "classnames";
import Link from "next/link";

const ContactPage = () => {
    return (
        <div
            className={cx(
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-gray-100 font-sans"
            )}
        >
            {/* Header */}
            <div className="text-center py-10 px-6">
                <h1 className="text-4xl md:text-6xl font-display font-bold text-white tracking-tight">
                    Contact Us
                </h1>
                <p className="mt-6 text-lg text-white/90 md:text-xl">
                    Get in touch with Detail On The Go
                </p>
            </div>

            {/* Contact Information */}
            <div className="w-full max-w-6xl mx-auto mt-10 space-y-6 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm rounded-xl p-6">

                {/* Main Contact Info */}
                <div className="text-center space-y-6 py-8">
                    <div className="space-y-4">
                        <h2 className="text-2xl font-bold text-white">
                            We're Here to Help! 📧
                        </h2>
                        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                            Have questions, concerns, or special requests? We'd love to hear from you and provide the best possible service.
                        </p>
                    </div>

                    {/* Email Contact */}
                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                        <div className="flex items-center justify-center space-x-3 mb-4">
                            <span className="text-3xl">✉️</span>
                            <h3 className="text-xl font-semibold text-white">Email Us</h3>
                        </div>
                        <a
                            href="mailto:<EMAIL>"
                            className="text-pink-400 hover:text-pink-300 transition-colors text-lg font-semibold"
                        >
                            <EMAIL>
                        </a>
                        <p className="text-gray-300 mt-2">
                            We typically respond within 24 hours
                        </p>
                    </div>

                    {/* Response Time */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                            <div className="flex items-center justify-center space-x-2 mb-3">
                                <span className="text-2xl">⚡</span>
                                <h4 className="text-lg font-semibold text-white">Quick Response</h4>
                            </div>
                            <p className="text-gray-300 text-sm">
                                Most emails are answered within a few hours during business hours
                            </p>
                        </div>

                        <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-6 rounded-xl border border-white/30">
                            <div className="flex items-center justify-center space-x-2 mb-3">
                                <span className="text-2xl">🤝</span>
                                <h4 className="text-lg font-semibold text-white">Personal Service</h4>
                            </div>
                            <p className="text-gray-300 text-sm">
                                Every message gets personal attention from our team
                            </p>
                        </div>
                    </div>
                </div>

                {/* FAQ Reference */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl text-center border border-white/30">
                    <div className="space-y-4">
                        <h2 className="text-2xl font-bold text-white flex items-center justify-center space-x-2">
                            <span>❓</span>
                            <span>Quick Answers</span>
                        </h2>
                        <p className="text-gray-300 max-w-2xl mx-auto">
                            Looking for quick answers? Check out our frequently asked questions for instant help with common inquiries about our services, pricing, and policies.
                        </p>
                        <Link
                            href="/faq"
                            className="inline-block bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-colors transform hover:scale-105"
                        >
                            Visit Our FAQ Page →
                        </Link>
                    </div>
                </div>

                {/* Contact Types */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">📅</span>
                        <h4 className="text-white font-semibold mb-1">Booking Issues</h4>
                        <p className="text-gray-300 text-sm">Schedule changes, cancellations, or booking problems</p>
                    </div>

                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">💼</span>
                        <h4 className="text-white font-semibold mb-1">Service Questions</h4>
                        <p className="text-gray-300 text-sm">Questions about our detailing packages and services</p>
                    </div>

                    <div className="bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-4 rounded-xl border border-white/30 text-center">
                        <span className="text-2xl mb-2 block">💬</span>
                        <h4 className="text-white font-semibold mb-1">General Inquiries</h4>
                        <p className="text-gray-300 text-sm">Any other questions or feedback you'd like to share</p>
                    </div>
                </div>

                {/* Phone Contact Section */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl border border-white/30">
                    <div className="text-center space-y-6">
                        <div className="flex items-center justify-center space-x-3">
                            <span className="text-3xl">📞</span>
                            <h2 className="text-2xl font-bold text-white">Call Us Directly</h2>
                        </div>
                        <div className="space-y-4">
                            <a
                                href="tel:+***********"
                                className="text-pink-400 hover:text-pink-300 transition-colors text-2xl font-bold block"
                            >
                                (*************
                            </a>
                            <p className="text-gray-300 max-w-2xl mx-auto">
                                Prefer to talk? Give us a call! We're available during business hours to answer questions,
                                schedule appointments, or discuss your specific detailing needs. Our friendly team is ready
                                to help you get your vehicle looking its absolute best.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Service Areas */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl border border-white/30">
                    <div className="text-center space-y-6">
                        <div className="flex items-center justify-center space-x-3">
                            <span className="text-3xl">🗺️</span>
                            <h2 className="text-2xl font-bold text-white">Service Areas</h2>
                        </div>
                        <p className="text-gray-300 max-w-3xl mx-auto">
                            Detail On The Go proudly serves multiple locations across the United States.
                            We bring our professional mobile detailing services directly to your location,
                            whether that's your home, office, or any convenient spot.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                            <div className="bg-white/10 p-4 rounded-lg">
                                <h4 className="text-white font-semibold mb-2">Kansas</h4>
                                <p className="text-gray-300 text-sm">Lawrence, Kansas City</p>
                            </div>
                            <div className="bg-white/10 p-4 rounded-lg">
                                <h4 className="text-white font-semibold mb-2">Missouri</h4>
                                <p className="text-gray-300 text-sm">Kansas City, St. Louis West</p>
                            </div>
                            <div className="bg-white/10 p-4 rounded-lg">
                                <h4 className="text-white font-semibold mb-2">Texas</h4>
                                <p className="text-gray-300 text-sm">Dallas-Fort Worth, Houston</p>
                            </div>
                            <div className="bg-white/10 p-4 rounded-lg">
                                <h4 className="text-white font-semibold mb-2">Other States</h4>
                                <p className="text-gray-300 text-sm">Denver CO, Phoenix AZ, New York</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Business Hours */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl border border-white/30">
                    <div className="text-center space-y-6">
                        <div className="flex items-center justify-center space-x-3">
                            <span className="text-3xl">🕒</span>
                            <h2 className="text-2xl font-bold text-white">Business Hours</h2>
                        </div>
                        <p className="text-gray-300 max-w-2xl mx-auto">
                            We understand that your schedule is busy, which is why we offer flexible appointment times
                            to accommodate your needs. Our mobile service means we come to you at your convenience.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div className="bg-white/10 p-6 rounded-lg">
                                <h4 className="text-white font-semibold mb-3">Peak Season (March - November)</h4>
                                <div className="space-y-2 text-gray-300">
                                    <p>Monday - Saturday: 8:00 AM - 6:00 PM</p>
                                    <p>Sunday: By appointment</p>
                                    <p className="text-sm text-pink-300 mt-2">Extended hours available for special requests</p>
                                </div>
                            </div>
                            <div className="bg-white/10 p-6 rounded-lg">
                                <h4 className="text-white font-semibold mb-3">Winter Season (December - February)</h4>
                                <div className="space-y-2 text-gray-300">
                                    <p>Monday - Friday: 9:00 AM - 5:00 PM</p>
                                    <p>Saturday: 9:00 AM - 3:00 PM</p>
                                    <p className="text-sm text-pink-300 mt-2">Weather-dependent scheduling</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Emergency Contact */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl border border-white/30">
                    <div className="text-center space-y-6">
                        <div className="flex items-center justify-center space-x-3">
                            <span className="text-3xl">🚨</span>
                            <h2 className="text-2xl font-bold text-white">Need Immediate Assistance?</h2>
                        </div>
                        <p className="text-gray-300 max-w-2xl mx-auto">
                            For urgent scheduling changes, same-day service requests, or time-sensitive issues,
                            don't hesitate to call us directly. We'll do our best to accommodate emergency
                            detailing needs and last-minute schedule adjustments.
                        </p>
                        <div className="bg-white/10 p-4 rounded-lg inline-block">
                            <p className="text-pink-300 font-semibold">
                                Emergency Contact: <a href="tel:+***********" className="hover:text-pink-200">(*************</a>
                            </p>
                        </div>
                    </div>
                </div>

                {/* Alternative Contact Methods */}
                <div className="mt-12 bg-[rgba(15,55,255,0.9)] backdrop-blur-sm p-8 rounded-xl border border-white/30">
                    <div className="text-center space-y-6">
                        <div className="flex items-center justify-center space-x-3">
                            <span className="text-3xl">💬</span>
                            <h2 className="text-2xl font-bold text-white">Other Ways to Reach Us</h2>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="bg-white/10 p-6 rounded-lg">
                                <h4 className="text-white font-semibold mb-3">Business Inquiries</h4>
                                <p className="text-gray-300 text-sm mb-2">For franchise opportunities, partnerships, or business-related questions:</p>
                                <a href="mailto:<EMAIL>" className="text-pink-400 hover:text-pink-300 text-sm">
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="bg-white/10 p-6 rounded-lg">
                                <h4 className="text-white font-semibold mb-3">Customer Service</h4>
                                <p className="text-gray-300 text-sm mb-2">For service questions, feedback, or support:</p>
                                <a href="mailto:<EMAIL>" className="text-pink-400 hover:text-pink-300 text-sm">
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="bg-white/10 p-6 rounded-lg">
                                <h4 className="text-white font-semibold mb-3">Online Booking</h4>
                                <p className="text-gray-300 text-sm mb-2">Schedule your service online with our EasyBook system:</p>
                                <Link href="/services" className="text-pink-400 hover:text-pink-300 text-sm">
                                    Book Now →
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactPage;
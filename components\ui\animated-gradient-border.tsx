import React, { CSSProperties, ReactNode, HTMLAttributes } from 'react';

type AnimationMode = 'auto-rotate' | 'rotate-on-hover' | 'stop-rotate-on-hover';

interface BorderRotateProps extends Omit<HTMLAttributes<HTMLDivElement>, 'className'> {
    children: ReactNode;
    className?: string;

    // Animation customization
    animationMode?: AnimationMode;
    animationSpeed?: number; // Duration in seconds

    // Color customization
    gradientColors?: {
        primary: string;
        secondary: string;
        accent: string;
    };
    backgroundColor?: string;

    // Border customization
    borderWidth?: number;
    borderRadius?: number;

    // Container styling
    style?: CSSProperties;
}

// Your website's color scheme
const defaultGradientColors = {
    primary: '#0026ff',     // Your main blue
    secondary: '#0d41e1',   // palatinate_blue
    accent: '#fa61ff'       // Your pink accent
};

const BorderRotate: React.FC<BorderRotateProps> = ({
    children,
    className = '',
    animationMode = 'auto-rotate',
    animationSpeed = 3,
    gradientColors = defaultGradientColors,
    backgroundColor = 'rgba(255, 255, 255, 0.7)', // Matches your solid white sections
    borderWidth = 2,
    borderRadius = 16,
    style = {},
    ...props
}) => {
    // Get animation class based on mode
    const getAnimationClass = () => {
        switch (animationMode) {
            case 'auto-rotate':
                return 'gradient-border-auto';
            case 'rotate-on-hover':
                return 'gradient-border-hover';
            case 'stop-rotate-on-hover':
                return 'gradient-border-stop-hover';
            default:
                return '';
        }
    };

    const combinedStyle: CSSProperties = {
        '--gradient-primary': gradientColors.primary,
        '--gradient-secondary': gradientColors.secondary,
        '--gradient-accent': gradientColors.accent,
        '--bg-color': backgroundColor,
        '--border-width': `${borderWidth}px`,
        '--border-radius': `${borderRadius}px`,
        '--animation-duration': `${animationSpeed}s`,
        border: `${borderWidth}px solid transparent`,
        borderRadius: `${borderRadius}px`,
        backgroundImage: `
      linear-gradient(${backgroundColor}, ${backgroundColor}),
      conic-gradient(
        from var(--gradient-angle, 0deg),
        ${gradientColors.primary} 0%,
        ${gradientColors.secondary} 25%,
        ${gradientColors.accent} 50%,
        ${gradientColors.secondary} 75%,
        ${gradientColors.primary} 100%
      )
    `,
        backgroundClip: 'padding-box, border-box',
        backgroundOrigin: 'padding-box, border-box',
        ...style,
    } as CSSProperties;

    return (
        <div
            className={`gradient-border-component ${getAnimationClass()} ${className}`}
            style={combinedStyle}
            {...props}
        >
            {children}
        </div>
    );
};

export { BorderRotate };
"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import BookingSection from "@/components/BookingSection";

interface SlideshowProps {
  images: string[];
  index: number;
}

const Slideshow = ({ images, index }: SlideshowProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const initialDelay = index * 500;
    const timer = setTimeout(() => {
      const interval = setInterval(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
      }, 3000);
      return () => clearInterval(interval);
    }, initialDelay);

    return () => clearTimeout(timer);
  }, [images.length, index]);

  return (
    <div className="relative h-40 overflow-hidden">
      {images.map((src: string, imgIndex: number) => (
        <Image
          key={src}
          src={src}
          alt={`Slide ${imgIndex}`}
          fill
          className={`object-cover transition-opacity duration-1000 ease-in-out ${imgIndex === currentImageIndex ? "opacity-100" : "opacity-0"
            }`}
          style={{ position: "absolute" }}
        />
      ))}
    </div>
  );
};

export default function Home() {
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [videoDelays, setVideoDelays] = useState<number[]>([]);

  useEffect(() => {
    const delays = ['Car', 'RV', 'Boat'].map(() => Math.floor(Math.random() * 5000));
    setVideoDelays(delays);
  }, []);

  return (
    <main id="main-content" className="flex flex-col items-center min-h-screen space-y-8 font-sans">

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-4 flex justify-between items-center border-b">
              <h2 className="text-xl font-bold tracking-tighter">Booking Details</h2>
              <button
                onClick={() => setShowBookingModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <BookingSection onClose={() => setShowBookingModal(false)} />
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section className="w-full py-5 md:pt-16 md:pb-4">
        <div className="max-w-6xl mx-auto px-2 text-center">
          <h1 className="mb-2 text-6xl md:text-6xl font-bold text-white tracking-tighter">
            We're Your Mobile Detailers
          </h1>
          <p className="text-lg text-white/90 max-w-3xl mx-auto mb-4">
            Your go‑to mobile detailing experts for cars, RVs, boats, ceramic coatings, paintless dent removal, and more.
          </p>
          {/* Modified button container to always center the button */}
          <div className="flex justify-center px-8">
            <div className="relative">
              {/* Added z-index-30 to ensure the emoji is on top */}
              <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                <span className="inline-block wave-animation">👉</span>
              </span>
              <button
                onClick={() => setShowBookingModal(true)}
                className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
              >
                <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                  I'M READY FOR A BEAUTIFUL DETAIL
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Video Section */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#000edf" }}>
        <div className="flex justify-start mb-6">
          <h2 className="text-white font-bold text-3xl md:text-5xl tracking-tighter">
            Our Specialty
          </h2>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center">
          {['Car', 'RV', 'Boat'].map((type, idx) => (
            <div
              key={type}
              className="group overflow-hidden relative rounded-xl w-80"
              style={{ backgroundColor: "rgba(15, 55, 255, 0.9)" }}
            >
              <div className="relative pb-[125%] rounded-t-xl overflow-hidden">
                {videoDelays[idx] !== undefined && (
                  <iframe
                    src={`https://www.youtube.com/embed/${['WKt0FC22zi0', 'toKFrlK-ets', 'b_L4xxt-lkk'][idx]}?autoplay=1&mute=1&loop=1&playlist=${['WKt0FC22zi0', 'toKFrlK-ets', 'b_L4xxt-lkk'][idx]}&controls=0&modestbranding=1&start=${Math.floor(videoDelays[idx] / 1000)}`}
                    className="absolute w-full h-full object-cover"
                    title={`${type} Detailing`}
                    allow="autoplay; encrypted-media"
                  />
                )}
                <div className="absolute inset-0 z-10 pointer-events-auto"></div>
              </div>
              <div className="p-4 bg-transparent">
                <h3 className="text-lg font-semibold text-white text-center tracking-tighter">
                  {type.toUpperCase()} DETAILING
                </h3>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#000edf" }}>
        <div className="flex justify-start mb-4">
          <h2 className="text-white font-bold text-3xl md:text-5xl tracking-tighter">
            Why Our Clients Choose Us Everytime
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center">
          {whyChooseUs.map((item, index) => (
            <div key={index} className="p-4 rounded-xl shadow-sm hover:shadow-md transition-shadow border border-white/30 w-80" style={{ backgroundColor: "#000edf" }}>
              <div className="text-4xl mb-4 text-white">{item.icon}</div>
              <h3 className="text-lg font-semibold mb-2 text-white tracking-tighter">{item.title}</h3>
              <p className="text-sm text-white/90">{item.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Services */}
      <section className="py-7 px-12 mb-8 border-2 border-white rounded-xl w-fit mx-auto" style={{ backgroundColor: "#000edf" }}>
        <div className="flex justify-start mb-4">
          <h2 className="text-white font-bold text-3xl md:text-5xl tracking-tighter">
            All Our Services Include 
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center">
          {carServices.map((service, index) => (
            <div key={index} className="rounded-xl border border-white/30 overflow-hidden hover:shadow-lg transition-shadow w-80" style={{ backgroundColor: "#000edf" }}>
              <Slideshow images={service.images} index={index} />
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2 text-white tracking-tighter">{service.title}</h3>
                <p className="text-sm text-white/90 mb-4">{service.description}</p>
                <button
                  onClick={() => setShowBookingModal(true)}
                  className="text-pink-400 font-semibold hover:text-pink-300 transition-colors text-sm"
                >
                  Learn More →
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Franchise CTA */}
      <section className="w-full py-16 px-2 mb-8">
        <div className="max-w-6xl mx-auto px-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-0 md:gap-0 relative">
            {/* Join Our Detailing Team Section */}
            <div className="bg-gradient-to-br from-pink-500 to-pink-600 text-white py-8 px-6 relative z-10 md:rounded-l-lg">
              <div className="text-center">
                <h2 className="text-3xl font-bold mb-6 tracking-tighter">
                  Join Our Detailing Team
                </h2>
                <p className="text-lg mb-8 max-w-2xl mx-auto text-white/90">
                  Work with the best in mobile detailing. Grow your skills and career with Detail On The Go.
                </p>
                <div className="relative inline-block">
                  <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                  </span>
                  <Link
                    href="/help-wanted"
                    className="group relative bg-white text-pink-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 hover:-translate-y-[5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                  >
                    <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                      EXPLORE TEAM OPPORTUNITIES
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                  </Link>
                </div>
              </div>
              {/* Slanted edge for desktop, hidden on mobile */}
              <div className="hidden md:block absolute top-0 right-0 w-12 h-full bg-gradient-to-br from-pink-500 to-pink-600 transform -skew-x-12 translate-x-6 z-10"></div>
            </div>
            {/* Start Your Detailing Business Section */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-600 text-white py-8 px-6 md:rounded-r-lg">
              <div className="text-center">
                <h2 className="text-3xl font-bold mb-6 tracking-tighter">
                  Start Your Detailing Business
                </h2>
                <p className="text-lg mb-8 max-w-2xl mx-auto text-white/90">
                  Low investment, high returns. Join our proven franchise model today.
                </p>
                <div className="relative inline-block">
                  <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                  </span>
                  <Link
                    href="/franchise"
                    className="group relative bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 hover:-translate-y-[5px] hover:shadow-lg hover:shadow-blue-500 shiny-button border border-white"
                  >
                    <span className="relative z-10 italic font-bold text-xl tracking-tighter">
                      EXPLORE FRANCHISE OPPORTUNITIES
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-gray-200 to-gray-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                  </Link>
                </div>
              </div>
            </div>

            {/* Mobile divider */}
            <div className="md:hidden h-1 bg-gradient-to-r from-pink-500 to-blue-500 my-4"></div>
          </div>
        </div>
        <style jsx>{`
                .wave-animation {
                    animation: wave 1.5s infinite;
                }
                @keyframes wave {
                    0%, 100% { transform: translateX(0); }
                    50% { transform: translateX(10px); }
                }
            `}</style>
      </section>

    </main>
  );
}

// Updated carServices with slideshow images
const carServices = [
  {
    title: "Interior Detailing",
    description: "Detail On The Go guarantees that your car's interior is left plush, fluffy-fresh, clean, smooth and sanitized. Dust, dirt, grime, french fries, crumbs, and any other of those weird sticky-icky substances are annihilated with our rigorous interior detailing.",
    includes: "Vacuuming, Steaming, Wipe-down, Glass Cleaning, and more",
    images: [
      "/interior detailing 1.png",
      "/interior detailing 2.png",
      "/interior detailing 3.jpg",
      "/interior detailing 4.png",
      "/interior detailing 5.png",
    ],
  },
  {
    title: "Exterior Detailing",
    description: "There are hundreds of bugs, thousands of metal deposits and millions of dust particles waiting to permanently etch and scratch into your car's pristine exterior. Our experts use expensive and professional auto detailing products and equipment on your vehicle to make sure it receives the finest care.",
    includes: "Soft rinse, pH-Neutral foam/ceramic foam, hand wash, post rinse, microfiber towel dry",
    images: [
      "/exterior wash 1.jpg",
      "/exterior wash 2.jpg",
      "/exterior wash 3.jpg",
      "/exterior wash 4.jpg",
      "/exterior wash 5.png",
    ],
  },
  {
    title: "Buffing & Polishing | Paint Correction",
    description: "Detail On The Go specializes in removing car swirls, scratches, scuffs and blemishes. Our service brings the fountain of youth to your car's exterior, making it shine bright like a diamond.",
    includes: "Brings back that smooth, slick surface shine like the day it came out the factory",
    images: [
      "/polishing 1.jpg",
      "/polishing 2.jpg",
    ],
  },
  {
    title: "Ceramic Coating",
    description: "Safeguard your vehicle from swirl marks, oxidation, scratches, and the possibility of rust formation by use of our advanced ceramic coating packages and application.",
    includes: "Ceramic Coating is a liquid polymer that chemically bonds with the factory paint, creating a durable and protective layer",
    images: [
      "/ceramic1.jpg",
      "/ceramic2.jpg",
      "/ceramic3.jpg",
      "/ceramic4.png",
    ],
  },
  {
    title: "Paintless Dent Removal",
    description: "Restore your vehicle's flawless appearance and maintain its factory finish with our expert Paintless Dent Repair (PDR) services.",
    includes: "PDR is a specialized technique that removes dents and dings without the need for paint or fillers, preserving the original integrity of your vehicle's paint job while offering a cost-effective and eco-friendly solution",
    images: [
      "/Paintless-Dent-Repair.jpg", // Assuming this might be relevant; adjust if needed
    ],
  },
  {
    title: "Auto Glass Repair and Replacement",
    description: "We are your mobile  windshield repair company. We specialize in chip repair, preventing long cracks and unnecessary replacement cost.",
    includes: "Jake Morby 'Owner & Operator' takes great pride in the quality of his work and has a reputation to fulfill. He knows integrity is key.  KC Rock Chips is a small locally owned business.",
    images: [
      "/rock chip 3.png", // Assuming this might be relevant; adjust if needed
    ],
  },
  {
    title: "Mobile Oil Change, Fluid Top Off and Tire Check",
    description: "Restore your vehicle's flawless appearance and maintain its factory finish with our expert Paintless Dent Repair (PDR) services.",
    includes: "PDR is a specialized technique that removes dents and dings without the need for paint or fillers, preserving the original integrity of your vehicle's paint job while offering a cost-effective and eco-friendly solution",
    images: [
      "/oil change 3.png", // Assuming this might be relevant; adjust if needed
    ],
  },
];

const whyChooseUs = [
  {
    icon: "🚗",
    title: "Mobile Service",
    description: "We come to you, providing convenient detailing at your location."
  },
  {
    icon: "⭐",
    title: "Premium Quality",
    description: "Using top-tier products and techniques for exceptional results."
  },
  {
    icon: "⏰",
    title: "Time Efficient",
    description: "Fast, reliable service without compromising quality."
  }
];
/* eslint-disable @next/next/no-img-element */
import { ImageResponse } from "next/og";

export const runtime = "edge";
export const alt = "Detail On The Go - Professional Mobile Car, Boat and RV Detailing";
export const contentType = "image/png";

export default async function OG() {
  return new ImageResponse(
    (
      <div
        style={{
          height: "100%",
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "white",
          backgroundImage:
            "linear-gradient(to bottom right, #0d41e1 25%, #0a85ed 50%, #07c8f9 75%)",
        }}
      >
        <img
          src={`https://${process.env.VERCEL_URL || "detailonthego.com"}/logo.png`}
          alt="Detail On The Go Logo"
          tw="w-20 h-20 mb-4 opacity-95"
        />
        <h1
          style={{
            fontSize: "80px",
            fontFamily: "system-ui, -apple-system, Arial, sans-serif",
            background:
              "linear-gradient(to bottom right, #ffffff 21.66%, #f3f4f6 86.47%)",
            backgroundClip: "text",
            color: "transparent",
            lineHeight: "4rem",
            letterSpacing: "-0.04em",
            textAlign: "center",
            fontWeight: "bold",
          }}
        >
          Detail On The Go
        </h1>
        <p
          style={{
            fontSize: "32px",
            fontFamily: "system-ui, -apple-system, Arial, sans-serif",
            color: "#ffffff",
            marginTop: "16px",
            textAlign: "center",
            fontWeight: "500",
            letterSpacing: "-0.02em",
          }}
        >
          Professional Mobile Detailing
        </p>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    },
  );
}
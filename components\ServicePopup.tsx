import React, { useState, useRef, useEffect } from "react";

// Add this comprehensive description map at the top of the file
const SERVICE_DESCRIPTIONS: { [key: string]: string } = {
    // Car Packages
    "Interior Detailing": `
    • Comprehensive vacuuming of every surface
    • Deep cleansing of upholstery and carpets to remove stubborn dirt and stains
    • Premium leather conditioning to prevent cracking and maintain softness
    • Detailed dashboard and console cleaning
    • Thorough door panel and cup holder sanitization
    • Crystal-clear interior window and mirror cleaning
    • Sanitizing steam cleaning for a fresh, like-new feel

    Why Choose This? Perfect for those who want to restore their car's interior to showroom condition or prepare for a special occasion. Ideal for eliminating odors, allergens, and everyday wear and tear.`,

    "Exterior Detailing": `
    • Precision high-pressure pre-rinse to remove loose debris
    • Premium pH-balanced soap wash for gentle yet effective cleaning
    • Comprehensive wheel and tire degreasing and cleaning
    • Hand drying with ultra-soft 2000 GSM microfiber towels to prevent scratching
    • Protective hand spray wax application (provides 2-month surface protection)
    • Professional tire and trim dressing for a sleek, renewed appearance
    • Steam window cleaning for streak-free clarity
    • Detailed door jambs cleaning

    Why Choose This? Perfect for car enthusiasts who want their vehicle to shine and protect its paint from environmental damage. Great for maintaining your car's value and making a stellar first impression.`,

    "Interior & Exterior Detailing": `
    • Total interior deep cleaning:
      - Comprehensive vacuuming
      - Upholstery and carpet deep cleaning
      - Leather conditioning
      - Dashboard and console detailing
      - Door panel and cup holder sanitization
      - Interior window and mirror cleaning
      - Thorough steam cleaning
    • Exterior brilliance:
      - High-pressure pre-rinse
      - pH-balanced soap wash
      - Wheel and tire comprehensive cleaning
      - Hand drying with premium microfiber towels
      - Protective spray wax (2-month surface protection)
      - Tire and trim professional dressing
      - Steam window cleaning
      - Detailed door jambs cleaning

    Why Choose This? The ultimate solution for those wanting a complete vehicle rejuvenation. Perfect for selling your car, preparing for a special event, or simply treating your vehicle to a five-star spa experience.`,

    // Car Addons
    "Window Shield Ceramic Coating": `
    • Powerful 1-year hydrophobic shield
    • Blocks harmful UV radiation
    • Dramatically improves rainy day visibility
    • Prevents mineral deposit buildup
    • Creates a barrier against bug splatter

    Why Choose This? For drivers who prioritize safety and want crystal-clear visibility in all weather conditions. Ideal for those who frequently drive in rain, dusty, or bug-prone areas.`,

    "Full Body Ceramic Coating": `
    • Advanced SiO2 formula for ultimate defense
    • Enhances paint color depth and glossiness
    • Provides robust resistance to chemical stains
    • Self-cleaning surface technology
    • Recommended after initial paint correction

    Why Choose This? Perfect for car enthusiasts and those wanting to preserve their vehicle's showroom finish. Protects your investment and keeps your car looking pristine with minimal maintenance.`,

    "Scratch Removal / Per Body Panel": `
    • Advanced paint leveling technique
    • Comprehensive 3-stage compounding and polishing process
    • Customized per-panel pricing
    • Protective final wax coating

    Why Choose This? Ideal for boat owners looking to restore their vessel's appearance and protect against further damage. Perfect for removing minor scratches and restoring surface shine.`,

    "Headlight Restoration": `
    • Complete oxidation removal process
    • Professional UV-resistant clear coat application
    • Precision sanding from 600 to 3000 grit
    • Professional-grade polishing compound
    • Comprehensive waterproof sealing

    Why Choose This? For vehicle owners experiencing reduced nighttime visibility or cloudy headlights. Restores clarity, improves safety, and enhances your vehicle's aesthetic appeal.`,

    "Paint Correction": `
    • Precision single-step machine polishing
    • Comprehensive swirl mark elimination
    • Ensures a flawless, hologram-free finish
    • Recommended ceramic coating follow-up for extended protection

    Why Choose This? Perfect for those wanting to restore their vehicle's paint to pristine condition. Ideal before selling, showcasing, or simply reviving your vehicle's original shine.`,

    "Window Rock Chip Repair": `
    • Precise resin injection technology
    • UV-cured bonding solution
    • Premium OEM-approved materials
    • Rapid repair process
    • Prevents further crack progression

    Why Choose This? For drivers wanting to quickly and effectively address minor windshield damage before it becomes a costly replacement. Saves time and money by preventing further damage.`,

    "Window Replacement": `
    • Choice of OEM or aftermarket glass
    • Precision factory-style urethane installation
    • Advanced calibration systems
    • Rain sensor preservation
    • ADAS camera recalibration

    Why Choose This? Essential for maintaining your vehicle's safety systems and structural integrity. Perfect for those needing a professional, comprehensive window replacement.`,

    "Engine Clean": `
    • Complete engine bay degreasing
    • Removal of built-up oil residue
    • Careful component cleaning
    • Supports extended component longevity

    Why Choose This? For vehicle owners who want to maintain peak engine performance, prevent potential issues, and extend their engine's life. Ideal for those who take pride in their vehicle's mechanical health.`,

    "Oil Change, Washer Fluid & Tire Inflate": `
    • On-location service for maximum convenience
    • High-quality synthetic oil
    • Complete filter replacement
    • Thorough drain plug service
    • Washer fluid replenishment
    • Responsible fluid disposal
    • Precise tire pressure adjustment

    Why Choose This? Perfect for busy individuals wanting comprehensive vehicle maintenance without the hassle of visiting a service center. Ensures your vehicle runs smoothly and safely.`,


    // Boat Services
    "Vacuum & Wipedown / Pressure Wash Interior": `Comprehensive Boat Interior Restoration
• Pressure wash, vacuum & steam clean all surfaces
• Thorough salt residue removal
• Intensive upholstery shampooing
• Targeted mold and mildew treatment
• Non-skid surface thorough cleaning
• Glass cleaning
• 3 Month interior protective wax seal

Why Choose This? Essential for boat owners wanting to maintain their vessel's interior, prevent long-term damage, and ensure a clean, fresh environment for every voyage.`,


    "Boat Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry": `
    • Deep wash, soft scrub and soft towel dry  
    • Light hand wax (3 month protection)
    • Chemical algea removal 

    Why Choose This? Perfect for Boat owners wanting to maintain their boat's exterior, remove light contaminants, and protect against future damage.`,

    "1 Step Deep Polish / Oxidation Removal / Heavy Algae Removal": `
    • Professional rotary buffer oxidation removal
    • Intensive heavy growth pressure treatment
    • Protective polymer marine sealing wax

    Why Choose This? Ideal for boat owners battling surface oxidation and algae growth. Restores your boat's surface, prevents further damage, and prepares it for water adventures.`,

    "Boat Ceramic Coating": `Marine Surface Shielding
    • 3-year saltwater environment protection
    • Advanced hydrophobic surface treatment
    • Cutting-edge UV inhibitor technology
    • Effortless cleaning
    • Delivers a mirror-like, showroom finish

    Why Choose This? Perfect for boat enthusiasts seeking long-lasting protection in harsh marine environments. Preserves your boat's appearance and defends against challenging maritime conditions.`,

    // RV Services
    "Vacuum & Wipedown Interior": `RV Interior Comprehensive Care
    • Cabin air filter replacement
    • Thorough appliance exterior cleaning
    • Cabinet interior sanitization
    • Professional window treatment
    • Targeted upholstery stain treatment
    • Dashboard UV protection

    Why Choose This? Ideal for RV owners wanting to maintain a clean, fresh, and well-preserved interior. Perfect before a trip or after long-term storage.`,

    // RV Addons
    "Roof Cleaning": `
    • Thorough pressure washing
    • Protective foam application
    • Intensive surface scrubbing
    • Surface protection treatment

    Why Choose This? Essential for preventing long-term roof damage and maintaining your RV's structural integrity.`,

    "RV Exterior Wash, Spray Wax & Towel Dry": `
    • Exterior pressure wash, foam, light scrub, and towel dry
    • Light hand wax (3 month protection)
    • Rim & wheel degreasing & cleaning
    • Glass cleaning
    (Roof cleaning seperate)

    Why Choose This? Perfect for RV owners wanting to restore their RV's exterior, remove light environmental buildup, and protect against future damage.`,

    "One Step Deep Polish / Oxidation Removal": `
    • Professional rotary buffer oxidation removal
    • Protective polymer RV sealing compound wax

    Why Choose This? Ideal for RV owners battling with a dull surface due to oxidation. Restores your boat's surface, prevents further damage, and prepares it for future adventures.`,


    "RV Ceramic Coating": `Ultimate RV Surface Protection
    • Comprehensive surface decontamination
    • Thorough wash and clay bar treatment
    • 9H ceramic coating on all paint, plastic, and glass surfaces
    • Advanced hydrophobic top layer
    • UV protection
    • Chemical resistance

    Why Choose This? For RV owners seeking long-lasting protection and a perpetually clean, glossy finish. Defends against environmental wear and maintains your RV's pristine appearance.`
};

const Slideshow = ({ images }: { images: string[] }): JSX.Element => {
    const [activeIndex, setActiveIndex] = React.useState(0);

    React.useEffect(() => {
        const interval = setInterval(() => {
            setActiveIndex((prev) => (prev + 1) % images.length);
        }, 3000);
        return () => clearInterval(interval);
    }, [images.length]);

    return (
        <div className="relative h-48 overflow-hidden rounded-lg">
            {images.map((img, index) => (
                <div
                    key={img}
                    className={`absolute inset-0 transition-opacity duration-1000 ${index === activeIndex ? 'opacity-100' : 'opacity-0'
                        }`}
                >
                    <img
                        src={img}
                        alt="Service example"
                        className="w-full h-full object-cover"
                    />
                </div>
            ))}
            <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1">
                {images.map((_, index) => (
                    <button
                        key={index}
                        className={`w-2 h-2 rounded-full ${index === activeIndex ? 'bg-blue-600' : 'bg-white/50'
                            }`}
                        onClick={() => setActiveIndex(index)}
                    />
                ))}
            </div>
        </div>
    );
};

type ServicePopupProps = {
    title: string;
    images: string[];
    onClose: () => void;
    warning?: string;  // Add warning prop
};



const ServicePopup: React.FC<ServicePopupProps> = ({ title, images, onClose, warning }) => {
    const description = SERVICE_DESCRIPTIONS[title] || 'Detailed description not available';

    // Split the description into lines
    const lines = description.split('\n');

    // Extract "Why Choose This?" (assumed to be the last line)
    const whyChooseThis = lines[lines.length - 1].trim();

    // Remaining lines are the title and features
    const features = lines.slice(0, -1);

    return (
        <div className="fixed inset-0 bg-blue-950 flex items-stretch z-[100] sm:items-center sm:bg-black sm:bg-opacity-90">
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-white overflow-y-auto sm:max-w-2xl sm:mx-auto sm:h-[90vh] sm:rounded-2xl sm:shadow-2xl">
                {/* Close Button */}
                <button
                    onClick={onClose}
                    className="sticky top-6 right-6 float-right z-10 px-4 py-2 m-4 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors shadow-md"
                    aria-label="Cancel"
                >
                    Exit
                </button>

                {/* Content Container */}
                <div className="p-6 pt-20 pb-24 sm:p-8 sm:pt-8">
                    {/* Header */}
                    <h2 className="text-3xl sm:text-4xl font-bold text-blue-900 mb-6">
                        {title}
                    </h2>

                    {/* Image Gallery */}
                    <div className="mb-8 -mx-6 sm:mx-0 sm:rounded-xl overflow-hidden">
                        <Slideshow images={images} />
                    </div>

                    {/* Warning Alert */}
                    {warning && (
                        <div className="mb-8 p-4 bg-amber-50 border-l-4 border-amber-400 rounded-lg animate-fade-in">
                            <div className="flex items-start gap-3">
                                <svg
                                    className="flex-shrink-0 w-6 h-6 text-amber-600 mt-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                    />
                                </svg>
                                <p className="text-amber-800 text-base leading-relaxed">
                                    {warning}
                                </p>
                            </div>
                        </div>
                    )}

                    {/* Description */}
                    <div className="space-y-4 text-blue-900">
                        {/* Render "Why Choose This?" as a paragraph first */}
                        <p className="text-lg sm:text-xl leading-relaxed font-medium animate-fade-in-up">
                            {whyChooseThis}
                        </p>

                        {/* Render title and features */}
                        {features.map((line, index) => {
                            const trimmedLine = line.trim();
                            if (trimmedLine.startsWith('•')) {
                                return (
                                    <div
                                        key={index}
                                        className="flex items-start gap-3 animate-fade-in-up"
                                        style={{ animationDelay: `${(index + 1) * 50}ms` }}
                                    >
                                        <div className="flex-shrink-0 w-2 h-2 mt-3 bg-blue-600 rounded-full" />
                                        <div className="text-lg sm:text-xl leading-relaxed font-medium">
                                            {trimmedLine.replace('•', '').trim()}
                                        </div>
                                    </div>
                                );
                            } else if (trimmedLine) {
                                return (
                                    <p
                                        key={index}
                                        className="text-lg sm:text-xl leading-relaxed font-medium animate-fade-in-up"
                                        style={{ animationDelay: `${(index + 1) * 50}ms` }}
                                    >
                                        {trimmedLine}
                                    </p>
                                );
                            }
                            return null;
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ServicePopup;
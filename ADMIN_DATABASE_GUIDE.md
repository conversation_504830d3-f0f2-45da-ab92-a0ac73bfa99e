# Database Schema Documentation for Admin Dashboard

## Overview

This document explains the new database-driven pricing and configuration system that replaces hardcoded values. The admin dashboard will interact with these Firebase collections to manage branch-specific pricing and services.

## 🏗️ Database Architecture

### New Collections (Safe - Won't Affect Existing Data)

```
📁 branchConfigs/        # Branch information and settings
📁 vehiclePackages/      # Global package definitions
📁 vehicleAddons/        # Global addon definitions  
📁 vehicleSizes/         # Global size definitions
📁 branchPricing/        # Branch-specific pricing overrides
```

### Existing Collections (Untouched)
```
📁 bookings/            # Customer bookings (unchanged)
📁 paymentLinks/        # Payment management (unchanged)
📁 users/               # User authentication (unchanged)
```

## 🔧 How It Works

### 1. **Global Definitions + Branch Overrides**
- **Global**: Base prices and configurations stored in `vehiclePackages`, `vehicleAddons`, `vehicleSizes`
- **Branch-Specific**: Overrides stored in `branchPricing/{branchId}`
- **Priority**: Branch overrides take precedence over global values

### 2. **Example: Interior Detailing Pricing**
```typescript
// Global definition in vehiclePackages/interior-detailing
{
  name: "Interior Detailing",
  basePrice: 249,  // Default for all branches
  availableBranches: ["lwr", "w-stl", "dvr", "ny"]
}

// Branch override in branchPricing/lwr
{
  packagePricing: {
    "interior-detailing": {
      customPrice: 269,  // Lawrence charges $269 instead of $249
      available: true
    }
  }
}

// Result: Lawrence customers see $269, other branches see $249
```

## 📊 Database Schema Details

### branchConfigs/{branchId}
```typescript
{
  id: "lwr",
  name: "Lawrence/KC",
  businessNumber: "+***********",
  calendarId: "c_864b1c...",
  employeeInfo: {
    name: "Levi Taylor",
    email: "<EMAIL>",
    number: "+***********"
  },
  operatingHours: {
    standard: { start: "8:00 AM", end: "6:00 PM" },
    summer: { start: "7:00 AM", end: "7:00 PM" },
    winter: { start: "9:00 AM", end: "5:00 PM" }
  },
  serviceArea: {
    radius: 30,
    cities: ["Lawrence", "Kansas City", "Topeka"]
  },
  enabledVehicleTypes: ["car", "boat", "rv"],
  active: true,
  timezone: "America/Chicago"
}
```

### vehiclePackages/{packageId}
```typescript
{
  id: "interior-detailing",
  name: "Interior Detailing",
  vehicleType: "car",
  basePrice: 249,
  description: "Complete interior cleaning and detailing",
  images: ["/interior detailing 1.png", ...],
  estimatedDuration: 3,
  requiresConsecutiveDays: false,
  weatherDependent: false,
  availableBranches: ["lwr", "w-stl", "dvr", "ny"],
  active: true,
  sortOrder: 1
}
```

### branchPricing/{branchId}
```typescript
{
  id: "lwr",
  packagePricing: {
    "interior-detailing": {
      customPrice: 269,    // Override global $249
      available: true
    },
    "exterior-detailing": {
      customPrice: 89,     // Override global $99
      available: true
    }
  },
  addonPricing: {
    "ceramic-coating": {
      customPrice: 650,    // Override global $600
      available: true
    }
  },
  sizeMultipliers: {
    "sedan": {
      customMultiplier: 1.05  // Override global 1.0
    }
  },
  updatedAt: timestamp,
  updatedBy: "admin-user-id"
}
```

## 🎯 Admin Interface Requirements

### 1. **Branch Management Page**
```
/admin/branches/
- List all branches
- Branch status (active/inactive)
- Quick access to pricing management
```

### 2. **Branch-Specific Pricing Page**
```
/admin/branches/{branchId}/pricing
- Show global prices vs branch-specific overrides
- Edit branch-specific pricing
- Bulk price updates
- Price history/audit trail
```

### 3. **Service Management Page**
```
/admin/branches/{branchId}/services
- Enable/disable packages per branch
- Manage service descriptions
- Upload/manage images
- Set estimated durations
```

### 4. **Global Settings Page**
```
/admin/global/
- Manage global package definitions
- Set default prices (applied to all branches)
- Vehicle size management
- Global addon management
```

## 🔨 Admin Interface Functions Needed

### Read Operations
```typescript
// Get branch pricing with overrides
getBranchPricing(branchId: string)

// Get all packages with branch-specific prices
getBranchPackagesWithPricing(branchId: string)

// Get global package definitions
getGlobalPackages()

// Get pricing history/audit log
getPricingHistory(branchId: string, packageId?: string)
```

### Write Operations
```typescript
// Update branch-specific pricing
updateBranchPackagePrice(branchId: string, packageId: string, newPrice: number)

// Update global package definition
updateGlobalPackage(packageId: string, updates: Partial<VehiclePackage>)

// Bulk price update
bulkUpdateBranchPricing(branchId: string, updates: BranchPricingUpdates)

// Enable/disable package for branch
togglePackageAvailability(branchId: string, packageId: string, available: boolean)
```

## 🔐 Admin Permissions

### Role-Based Access
```typescript
// Super Admin (can manage all branches)
role: "super-admin"
permissions: ["read:all", "write:all", "admin:all"]

// Branch Admin (can only manage assigned branch)
role: "branch-admin"
assignedBranch: "lwr"
permissions: ["read:branch", "write:branch-pricing", "read:global"]

// Branch Employee (read-only access to their branch)
role: "employee"  
assignedBranch: "lwr"
permissions: ["read:branch"]
```

### Permission Checks
```typescript
// Example permission check in admin interface
function canEditBranchPricing(user: AdminUser, branchId: string): boolean {
  if (user.role === 'super-admin') return true;
  if (user.role === 'branch-admin' && user.assignedBranch === branchId) return true;
  return false;
}
```

## 📊 Real-Time Updates

### Cache Invalidation
When admin updates pricing:
1. Update database
2. Clear cache for affected branch
3. Notify customer-facing app
4. Log change in audit trail

### Customer Impact
- Changes take effect immediately (within 5 minutes due to cache)
- No app restart required
- Seamless experience for customers

## 🧪 Testing Strategy

### 1. **Migration Testing**
```typescript
// Run migration script
import { runSafeMigration, testMigration } from './migrationScript';

await runSafeMigration();    // Migrate data
await testMigration();       // Verify migration
```

### 2. **Admin Interface Testing**
- Test price updates across all branches
- Verify permission-based access control
- Test bulk operations
- Validate real-time updates

### 3. **Customer-Facing Testing**
- Verify branch-specific pricing displays correctly
- Test booking flow with new pricing
- Confirm fallback to hardcoded data if database fails

## 🚀 Implementation Steps

### Phase 1: Database Setup (Current)
- ✅ Created database schema
- ✅ Built service layer with fallbacks
- ✅ Created migration script
- ✅ Updated pricing to $249 for Interior Detailing

### Phase 2: Admin Interface (Next)
- [ ] Build branch selection interface
- [ ] Create pricing management UI
- [ ] Implement permission system
- [ ] Add audit logging

### Phase 3: Customer Integration
- [ ] Update BookingSection to use database
- [ ] Add real-time price loading
- [ ] Remove hardcoded fallbacks
- [ ] Performance optimization

### Phase 4: Advanced Features
- [ ] Price history and analytics
- [ ] A/B testing capabilities
- [ ] Seasonal pricing automation
- [ ] Multi-region support

## 🆘 Support Information

### Database Functions Available
All functions are in `/lib/firebase/branchConfigService.ts`:

```typescript
// Reading data
getBranchConfig(branchId)
getBranchPackages(branchId, vehicleType)
getBranchVehicleSizes(branchId)
getCompleteBranchConfig(branchId)

// Writing data (admin only)
updateBranchPricing(branchId, updates, adminUserId)
upsertVehiclePackage(packageData, adminUserId)

// Cache management
clearBranchCache(branchId)
```

### Migration Functions
Migration functions in `/lib/firebase/migrationScript.ts`:

```typescript
runSafeMigration()    // Run the migration
testMigration()       // Test migration results
runMigrationFromConsole()  // Full migration + test
```

### Error Handling
- All functions include proper error handling
- Fallback to hardcoded data if database fails
- Detailed logging for debugging
- Graceful degradation

---

*This database schema is designed to be completely safe and won't affect your existing booking, payment, or user systems. The customer-facing app will continue to work normally during the admin interface development.*

"use client";
import React, { useState, useEffect } from "react";
import ShinyText from "@/components/ShinyText";

interface EmailPopupProps {
    onClose: () => void;
}

const EmailPopup: React.FC<EmailPopupProps> = ({ onClose }) => {
    const [email, setEmail] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const [submitSuccess, setSubmitSuccess] = useState(false);

    // Email validation
    useEffect(() => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        setIsValid(re.test(email));
    }, [email]);

    // Handle form submission
    const handleSubmit = async () => {
        setIsSubmitting(true);
        setSubmitError(null);

        try {
            // Attempt to add email to subscriber list
            const subscriberData = { email };
            const addSubscriberUrl = process.env.NEXT_PUBLIC_ADD_SUBSCRIBER;
            if (!addSubscriberUrl) {
                throw new Error("NEXT_PUBLIC_ADD_SUBSCRIBER is not defined");
            }
            const subscriberResponse = await fetch(addSubscriberUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(subscriberData),
            });
            if (!subscriberResponse.ok) {
                console.error("Failed to add subscriber:", subscriberResponse.statusText);
            }

            // Proceed with sending email notification
            const payload = {
                fromName: "New Franchise Lead",
                fromEmail: "<EMAIL>",
                to: "<EMAIL>",
                subject: `New Franchise Inquiry: ${email}`,
                message: `<p><strong>New lead email:</strong> ${email}</p>`
            };

            const res = await fetch('/api/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!res.ok) throw new Error("Failed to submit email");

            setSubmitSuccess(true);
            setTimeout(onClose, 2000); // Close after 2 seconds
        } catch (err) {
            console.error("Submission Error:", err);
            setSubmitError(err instanceof Error ? err.message : "Submission failed");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden border-2 border-white/20">
                {/* Header */}
                <div className="bg-gradient-to-r from-pink-500 to-blue-500 p-6 relative">
                    <button
                        onClick={onClose}
                        className="absolute top-4 right-4 text-white hover:text-pink-200 transition-colors text-xl"
                    >
                        ✕
                    </button>
                    <div className="text-center">
                        <ShinyText
                            text="Claim Your Free Franchise Guide"
                            speed={5}
                            className="text-2xl font-bold text-white"
                        />
                    </div>
                </div>

                {/* Body */}
                <div className="p-8 space-y-6">
                    {submitSuccess ? (
                        <div className="text-center">
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">🎉 Success!</h3>
                            <p className="text-gray-700">Check your email for the franchise guide</p>
                        </div>
                    ) : (
                        <>
                            <h3 className="text-xl text-center text-gray-900 font-semibold">
                                Get our exclusive detailing playbook & profit calculator
                            </h3>
                            <div className="space-y-4">
                                <input
                                    type="email"
                                    placeholder="Enter your best email..."
                                    className={`w-full p-4 rounded-lg border-2 text-gray-900 placeholder-gray-600 focus:ring-2 focus:ring-pink-500 ${isValid || !email ? 'border-gray-300' : 'border-red-500'
                                        }`}
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    disabled={isSubmitting}
                                />
                                <button
                                    onClick={handleSubmit}
                                    disabled={!isValid || isSubmitting}
                                    className={`w-full group relative bg-pink-500 text-white px-6 py-4 rounded-lg font-semibold 
                    transition-all transform ${!isSubmitting && 'hover:scale-105 hover:shadow-lg'} 
                    ${isValid ? 'hover:bg-pink-600' : 'opacity-50 cursor-not-allowed'}
                    shiny-button border-2 border-white`}
                                >
                                    <span className="relative z-10 italic font-bold text-xl">
                                        {isSubmitting ? 'SENDING...' : 'GET INSTANT ACCESS →'}
                                    </span>
                                    <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg" />
                                </button>
                            </div>
                            <p className="text-center text-sm text-gray-600">
                                Zero spam. Unsubscribe anytime.
                            </p>
                            {submitError && (
                                <p className="text-red-500 text-center text-sm">{submitError}</p>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmailPopup;
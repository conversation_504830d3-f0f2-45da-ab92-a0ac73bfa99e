import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy | Detail On The Go",
  description: "Learn how Detail On The Go collects, uses, and protects your personal information. Our comprehensive privacy policy explains your rights and our data practices.",
  openGraph: {
    title: "Privacy Policy | Detail On The Go",
    description: "Learn how Detail On The Go collects, uses, and protects your personal information. Our comprehensive privacy policy explains your rights and our data practices.",
    url: "https://www.detailongo.com/privacy/",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

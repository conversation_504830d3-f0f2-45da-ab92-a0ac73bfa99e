"use client";
import Image from "next/image";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";

const industries = [
    "Car, Boat or RV Detailing",
    "Paintless Dent Removal",
    "Mechanic",
    "Window Tinting",
    "Auto Glass Repair and Replacement",
    "Other",
];

const NewsletterPage = () => {
    const [email, setEmail] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
    const [otherIndustry, setOtherIndustry] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const [submitSuccess, setSubmitSuccess] = useState(false);

    // Email validation
    const validateEmail = (value: string) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        setIsValid(re.test(value));
    };

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value);
        validateEmail(e.target.value);
    };

    const handleIndustryChange = (industry: string) => {
        setSelectedIndustries((prev) => {
            const newSelected = prev.includes(industry)
                ? prev.filter((i) => i !== industry)
                : [...prev, industry];
            if (industry === "Other" && !newSelected.includes("Other")) {
                setOtherIndustry("");
            }
            return newSelected;
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (
            !isValid ||
            selectedIndustries.length === 0 ||
            (selectedIndustries.includes("Other") && otherIndustry.trim() === "")
        ) {
            setSubmitError("Please complete all required fields.");
            setTimeout(() => setSubmitError(null), 5000);
            return;
        }

        setIsSubmitting(true);
        setSubmitError(null);

        try {
            // Prepare subscriber data for Cloud Function
            const subscriberData = {
                email: email,
                industries: selectedIndustries
                    .map((ind) => (ind === "Other" ? `Other: ${otherIndustry}` : ind))
                    .join(", "),
            };

            // Send subscriber data to Google Cloud Function
            const addSubscriberUrl = process.env.NEXT_PUBLIC_ADD_SUBSCRIBER;
            if (!addSubscriberUrl) {
                throw new Error("NEXT_PUBLIC_ADD_SUBSCRIBER is not defined");
            }
            const addSubscriberRes = await fetch(addSubscriberUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(subscriberData),
            });

            if (!addSubscriberRes.ok) {
                throw new Error("Failed to add subscriber to Google Sheet");
            }

            // Prepare and send email notification
            const industriesList = selectedIndustries
                .map((ind) => (ind === "Other" ? `Other: ${otherIndustry}` : ind))
                .join(", ");
            const message = `<p><strong>New Subscriber:</strong> ${email}</p>
                           <p><strong>Industries:</strong> ${industriesList}</p>`;

            const payload = {
                fromName: "New Subscriber",
                fromEmail: "<EMAIL>",
                to: "<EMAIL>",
                subject: `New Subscriber: ${email}`,
                message: message,
            };

            const emailRes = await fetch("/api/send-email", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });

            if (!emailRes.ok) {
                throw new Error("Failed to send email notification");
            }

            // Reset form on success
            setSubmitSuccess(true);
            setEmail("");
            setSelectedIndustries([]);
            setOtherIndustry("");
            setTimeout(() => setSubmitSuccess(false), 5000);
        } catch (err) {
            console.error("Submission Error:", err);
            setSubmitError(
                err instanceof Error ? err.message : "Submission failed"
            );
            setTimeout(() => setSubmitError(null), 5000);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <main className="min-h-screen bg-transparent flex items-center justify-center">
            <div className="max-w-md mx-auto px-4 text-center">
                <div className="mb-6">
                    <Image
                        src="/levi-headshot.png"
                        alt="Levi, Founder of Detail On The Go"
                        width={120}
                        height={120}
                        className="rounded-full mx-auto border-2 border-white/50"
                    />
                    <p className="text-white/90 text-sm mt-2 font-semibold">
                        Levi Taylor - Owner of Detail On The Go
                    </p>
                </div>
                <h1 className="mb-6">
                    <ShinyText
                        text="Join My Email List"
                        speed={5}
                        className="text-4xl md:text-5xl font-display font-bold italic text-white tracking-tight"
                    />
                </h1>
                <p className="text-lg text-white/90 mb-8">It’s good, I swear.</p>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <input
                        type="email"
                        value={email}
                        onChange={handleEmailChange}
                        placeholder="Enter your email"
                        className={`w-full px-4 py-3 rounded-lg bg-white/90 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-500 ${isValid || !email ? "border-gray-300" : "border-red-500"
                            }`}
                        disabled={isSubmitting}
                        required
                    />
                    <div className="space-y-2 mt-4">
                        <p className="text-white/90 text-left">
                            What do you do?
                        </p>
                        {industries.map((industry) => (
                            <div key={industry} className="flex items-center">
                                <input
                                    type="checkbox"
                                    id={industry}
                                    value={industry}
                                    checked={selectedIndustries.includes(industry)}
                                    onChange={() => handleIndustryChange(industry)}
                                    className="mr-2"
                                />
                                <label htmlFor={industry} className="text-white/90">
                                    {industry}
                                </label>
                            </div>
                        ))}
                        {selectedIndustries.includes("Other") && (
                            <input
                                type="text"
                                value={otherIndustry}
                                onChange={(e) => setOtherIndustry(e.target.value)}
                                placeholder="Please specify"
                                className="w-full px-4 py-2 mt-2 rounded-lg bg-white/90 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-500"
                                required
                            />
                        )}
                    </div>
                    <button
                        type="submit"
                        disabled={
                            !isValid ||
                            selectedIndustries.length === 0 ||
                            (selectedIndustries.includes("Other") &&
                                otherIndustry.trim() === "") ||
                            isSubmitting
                        }
                        className={`group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold transition-all transform ${!isSubmitting && "hover:scale-105 hover:shadow-lg"
                            } ${isValid &&
                                selectedIndustries.length > 0 &&
                                (!selectedIndustries.includes("Other") ||
                                    otherIndustry.trim() !== "")
                                ? "hover:bg-pink-600"
                                : "opacity-50 cursor-not-allowed"
                            } border border-white`}
                    >
                        <span className="relative z-10 italic font-bold text-xl">
                            {isSubmitting ? "SENDING..." : "JOIN NOW"}
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                    </button>
                    {submitSuccess && (
                        <p className="text-green-400 text-sm mt-2">
                            Success! Check your email for the franchise guide.
                        </p>
                    )}
                    {submitError && (
                        <p className="text-red-400 text-sm mt-2">{submitError}</p>
                    )}
                </form>
            </div>
        </main>
    );
};

export default NewsletterPage;
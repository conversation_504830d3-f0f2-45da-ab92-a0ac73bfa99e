"use client";

import {
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useMemo,
    useState,
} from "react";
import {
    motion,
    AnimatePresence,
    Transition,
    type VariantLabels,
    type Target,
    type AnimationControls,
    type TargetAndTransition,
} from "framer-motion";

function cn(...classes: (string | undefined | null | boolean)[]): string {
    return classes.filter(Boolean).join(" ");
}

export interface RotatingTextRef {
    next: () => void;
    previous: () => void;
    jumpTo: (index: number) => void;
    reset: () => void;
}

export interface RotatingTextProps
    extends Omit<
        React.ComponentPropsWithoutRef<typeof motion.span>,
        "children" | "transition" | "initial" | "animate" | "exit"
    > {
    staticText: string;
    rotatingTexts: string[];
    transition?: Transition;
    initial?: boolean | Target | VariantLabels;
    animate?: boolean | VariantLabels | AnimationControls | TargetAndTransition;
    exit?: Target | VariantLabels;
    animatePresenceMode?: "sync" | "wait";
    animatePresenceInitial?: boolean;
    rotationInterval?: number;
    staggerDuration?: number;
    staggerFrom?: "first" | "last" | "center" | "random" | number;
    loop?: boolean;
    auto?: boolean;
    splitBy?: string;
    onNext?: (index: number) => void;
    mainClassName?: string;
    splitLevelClassName?: string;
    elementLevelClassName?: string;
    highlightClassName?: string;
}

const RotatingText = forwardRef<RotatingTextRef, RotatingTextProps>(
    (
        {
            staticText,
            rotatingTexts,
            transition = { type: "spring", damping: 25, stiffness: 300 },
            initial = { y: "100%", opacity: 0 },
            animate = { y: 0, opacity: 1 },
            exit = { y: "-120%", opacity: 0 },
            animatePresenceMode = "wait",
            animatePresenceInitial = false,
            rotationInterval = 2000,
            staggerDuration = 0,
            staggerFrom = "first",
            loop = true,
            auto = true,
            splitBy = "words",
            onNext,
            mainClassName,
            splitLevelClassName,
            elementLevelClassName,
            highlightClassName = "bg-yellow text-black px-2 rounded",
            ...rest
        },
        ref
    ) => {
        const [currentTextIndex, setCurrentTextIndex] = useState<number>(0);

        const elements = useMemo(() => {
            const currentText: string = rotatingTexts[currentTextIndex];
            return [{
                characters: [currentText],
                needsSpace: false
            }];
        }, [rotatingTexts, currentTextIndex]);

        const getStaggerDelay = useCallback(
            (index: number, totalChars: number): number => {
                return index * staggerDuration;
            },
            [staggerDuration]
        );

        const handleIndexChange = useCallback(
            (newIndex: number) => {
                setCurrentTextIndex(newIndex);
                if (onNext) onNext(newIndex);
            },
            [onNext]
        );

        const next = useCallback(() => {
            const nextIndex =
                currentTextIndex === rotatingTexts.length - 1
                    ? loop
                        ? 0
                        : currentTextIndex
                    : currentTextIndex + 1;
            if (nextIndex !== currentTextIndex) {
                handleIndexChange(nextIndex);
            }
        }, [currentTextIndex, rotatingTexts.length, loop, handleIndexChange]);

        useImperativeHandle(
            ref,
            () => ({
                next,
                previous: () => { },
                jumpTo: () => { },
                reset: () => { },
            }),
            [next]
        );

        useEffect(() => {
            if (!auto) return;
            const intervalId = setInterval(next, rotationInterval);
            return () => clearInterval(intervalId);
        }, [next, rotationInterval, auto]);

        return (
            <motion.span
                className={cn("inline-flex items-baseline", mainClassName)}
                {...rest}
                layout
                transition={transition}
            >
                <span className="mr-2">{staticText}</span>
                <AnimatePresence
                    mode={animatePresenceMode}
                    initial={animatePresenceInitial}
                >
                    <motion.div
                        key={currentTextIndex}
                        className="inline-block"
                        layout
                        aria-hidden="true"
                    >
                        {elements.map((wordObj, wordIndex) => (
                            <span
                                key={wordIndex}
                                className={cn("inline-flex", splitLevelClassName)}
                            >
                                {wordObj.characters.map((char, charIndex) => (
                                    <motion.span
                                        key={charIndex}
                                        initial={initial}
                                        animate={animate}
                                        exit={exit}
                                        transition={{
                                            ...transition,
                                            delay: getStaggerDelay(charIndex, wordObj.characters.length),
                                        }}
                                        className={cn(highlightClassName, elementLevelClassName)}
                                    >
                                        {char}
                                    </motion.span>
                                ))}
                            </span>
                        ))}
                    </motion.div>
                </AnimatePresence>
            </motion.span>
        );
    }
);

RotatingText.displayName = "RotatingText";
export default RotatingText;
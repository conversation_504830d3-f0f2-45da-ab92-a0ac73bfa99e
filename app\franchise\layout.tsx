import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Franchise Opportunities | Detail On The Go',
    description: 'Join Detail On The Go as a franchisee. Learn about our franchise opportunities, support, and how to start your own mobile detailing business.',
    keywords: [
        'franchise',
        'auto detailing franchise',
        'mobile detailing franchise',
        'Detail On The Go franchise',
        'business opportunity',
        'start a franchise',
        'car detailing franchise'
    ],
    alternates: {
        canonical: 'https://www.detailongo.com/franchise/',
    },
    openGraph: {
        title: 'Franchise Opportunities | Detail On The Go',
        description: 'Join Detail On The Go as a franchisee. Start your own mobile detailing business with our support.',
        url: 'https://www.detailongo.com/franchise/',
        images: [
            {
                url: '/images/franchise-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Franchise Opportunities'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Franchise Opportunities | Detail On The Go',
        description: 'Join Detail On The Go as a franchisee. Start your own mobile detailing business with our support.',
        images: ['/images/franchise-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Offer",
                        "name": "Franchise Opportunities",
                        "url": "https://detailongo.com/franchise/",
                        "description": "Join Detail On The Go as a franchisee. Learn about our franchise opportunities, support, and how to start your own mobile detailing business.",
                        "offeredBy": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/franchise-og.jpg",
                            "telephone": "******-615-6156"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}

"use client";
import { useState, useRef } from "react";
import Link from "next/link";
import React, { useEffect } from "react";

export default function GlobalHelpButton() {
    const [showPopup, setShowPopup] = useState(false);
    const popupRef = useRef<HTMLDivElement>(null);

    // Toggle popup visibility
    const togglePopup = () => {
        setShowPopup(!showPopup);
    };

    // Close popup when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
        if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
            setShowPopup(false);
        }
    };

    // Set up event listeners
    useEffect(() => {
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <>
            {/* Help Button */}
            <button
                onClick={togglePopup}
                className="fixed bottom-8 right-8 bg-palatinate_blue text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:bg-palatinate_blue/90 transition-all z-[60]"
            >
                Need Help?
            </button>

            {/* Popup */}
            {showPopup && (
                <div className="fixed inset-0 bg-black/50 z-[70] flex items-center justify-center p-4">
                    <div
                        ref={popupRef}
                        className="bg-white rounded-xl w-full max-w-md p-6 text-gray-800 animate-fade-in"
                    >
                        <h2 className="text-xl font-bold mb-4">Pricing & Availability</h2>

                        <div className="space-y-4 mb-6">
                            <p><strong>Our interior details start at $248</strong> (pet hair removal is extra)</p>
                            <p>We service <strong>Cars, Boats, and RVs</strong> - all at your location!</p>
                            <p>We're <strong>mobile only</strong> and come directly to you.</p>
                            <p>Check availability, exact pricing, and book using our booking system.</p>
                        </div>

                        <div className="flex flex-col space-y-3">
                            <button
                                onClick={() => {
                                    window.dispatchEvent(new Event('openBookingModal'));
                                    setShowPopup(false);
                                }}
                                className="bg-palatinate_blue text-white px-4 py-3 rounded-lg font-semibold hover:bg-palatinate_blue/90"
                            >
                                Check Availability & Book Now
                            </button>

                            <Link
                                href="/contact"
                                onClick={() => setShowPopup(false)}
                                className="border border-palatinate_blue text-palatinate_blue px-4 py-3 rounded-lg font-semibold text-center hover:bg-palatinate_blue/10"
                            >
                                Contact Your Local Detailer
                            </Link>

                            <button
                                onClick={() => setShowPopup(false)}
                                className="text-gray-500 hover:text-gray-700 mt-2"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}
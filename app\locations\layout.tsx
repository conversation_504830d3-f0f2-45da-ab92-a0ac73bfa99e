import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Service Areas | Detail On The Go',
    description: 'Detail On The Go provides mobile auto detailing services in multiple locations. See all our service areas and book your appointment today.',
    keywords: [
        'service areas',
        'auto detailing locations',
        'mobile detailing service area',
        'Detail On The Go locations',
        'car detailing near me',
        'boat detailing near me',
        'RV detailing near me'
    ],
    alternates: {
        canonical: 'https://www.detailongo.com/locations/',
    },
    openGraph: {
        title: 'Service Areas | Detail On The Go',
        description: 'Mobile auto detailing services available in multiple locations. Find your area and book today.',
        url: 'https://www.detailongo.com/locations/',
        images: [
            {
                url: '/images/locations-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Service Areas'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Service Areas | Detail On The Go',
        description: 'Mobile auto detailing services available in multiple locations. Find your area and book today.',
        images: ['/images/locations-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Service",
                        "name": "Mobile Auto Detailing",
                        "provider": {
                            "@type": "LocalBusiness",
                            "name": "Detail On The Go",
                            "url": "https://www.detailongo.com/",
                            "image": "https://www.detailongo.com/images/locations-og.jpg",
                            "telephone": "******-615-6156"
                        },
                        "areaServed": [
                            "Lawrence, KS",
                            "Topeka, KS",
                            "Kansas City, KS",
                            "Overland Park, KS",
                            "Olathe, KS"
                            // ...add more locations as needed
                        ],
                        "serviceType": [
                            "Car Detailing",
                            "Boat Detailing",
                            "RV Detailing",
                            "Ceramic Coating"
                        ],
                        "url": "https://www.detailongo.com/locations/"
                    })
                }}
            />
            {children}
        </>
    );
}

"use client";
import ShinyText from "@/components/ShinyText";

export default function Home() {
    return (
        <main id="main-content" className="flex flex-col items-center min-h-screen space-y-8">
            <section className="w-full py-5 md:pt-16 md:pb-4">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-2">
                        <ShinyText
                            text="Thanks for booking with Detail On The Go!"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-lg text-white/90 max-w-3xl mx-auto mb-4">
                        Please check your email for confirmation and details about your booking.
                    </p>

                </div>
            </section>


        </main >
    );
}

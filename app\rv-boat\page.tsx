"use client";

import { helvetica } from "../fonts";
import cx from "classnames";
import Link from "next/link";
import { useState } from "react";
import BookingSection from "@/components/BookingSection";
import Image from "next/image";

const rvServices = [
    {
        title: "RV Exterior Wash, Spray Wax & Towel Dry",
        description: "Complete exterior revival for your RV: power wash, spray wax, and hand towel dry to keep it looking showroom-new.",
        image: "/rv1.jpg",
        price: "Starting at $15"
    },
    {
        title: "Roof Cleaning",
        description: "Comprehensive roof cleaning for RVs to remove debris, mold, and mildew, extending the life of your roof sealant.",
        image: "/rvroof1.jpg",
        price: "Starting at $5"
    },
    {
        title: "One Step Deep Polish / Oxidation Removal",
        description: "Eliminate oxidation and restore gloss with our one-step deep polish, perfect for RV fiberglass exteriors.",
        image: "/rv polishing.png",
        price: "Starting at $18"
    },
    {
        title: "RV Ceramic Coating",
        description: "Long-lasting ceramic protection to guard your RV against UV rays, road salts, and environmental contaminants.",
        image: "/RV ceramic.png",
        price: "Contact for pricing"
    }
];

const boatServices = [
    {
        title: "Vacuum & Wipedown / Pressure Wash Interior",
        description: "Deep-clean every corner of your boat's interior with vacuuming, wipedown, and pressure wash for a refreshed cabin.",
        image: "/boatinterior1.jpg",
        price: "Starting at $10"
    },
    {
        title: "Boat Exterior Wash, Light Algae Removal, Spray Wax & Towel Dry",
        description: "Protect and shine your boat's hull with algae removal, spray wax application, and hand towel drying.",
        image: "/boat washing.png",
        price: "Starting at $15"
    },
    {
        title: "1 Step Deep Polish / Oxidation Removal / Heavy Algae Removal",
        description: "Aggressive polish and oxidation removal combined with heavy algae cleaning for a mirror-like finish.",
        image: "/boatpolishing.jpg",
        price: "Starting at $18"
    },
    {
        title: "Boat Ceramic Coating",
        description: "Ultimate marine-grade ceramic coating to protect against saltwater, UV, and oxidation for lasting gloss.",
        image: "/boat ceramic.png",
        price: "Starting at $50"
    }
];

export default function RVBoatServices() {
    const [showBookingModal, setShowBookingModal] = useState(false);

    return (
        <div
            className={cx(
                helvetica.variable,
                "pt-20 flex flex-col items-center min-h-screen space-y-8 p-4 sm:p-8 bg-transparent text-white font-sans"
            )}
        >
            {/* Booking Modal */}
            {showBookingModal && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                    <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                        <div className="p-4 flex justify-between items-center border-b">
                            <h2 className="text-xl font-black tracking-tighter text-gray-800">Book Your RV or Boat Detail</h2>
                            <button
                                onClick={() => setShowBookingModal(false)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                ✕
                            </button>
                        </div>
                        <BookingSection onClose={() => setShowBookingModal(false)} />
                    </div>
                </div>
            )}

            <div className="max-w-6xl mx-auto">
                {/* Hero Section */}
                <div className="text-center mb-16">
                    <h1 className="text-5xl md:text-6xl font-black tracking-tighter mb-6 text-white">
                        RV & Boat Detailing
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        Professional mobile detailing services for your RV and boat. We come to you with specialized equipment and expertise to keep your recreational vehicles looking their best.
                    </p>
                    <button
                        onClick={() => setShowBookingModal(true)}
                        className="inline-flex items-center px-8 py-4 bg-pink-500 text-white font-bold text-lg rounded-lg hover:bg-pink-600 transition-colors"
                    >
                        Book Your Detail Now →
                    </button>
                </div>

                {/* RV Services Section */}
                <section className="mb-16">
                    <div className="text-center mb-12">
                        <h2 className="text-4xl font-bold text-white mb-4">RV Detailing Services</h2>
                        <p className="text-lg text-white/80 max-w-2xl mx-auto">
                            Keep your home on wheels looking pristine with our comprehensive RV detailing services.
                        </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {rvServices.map((service, index) => (
                            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all">
                                <div className="relative h-48 mb-4 rounded-lg overflow-hidden">
                                    <Image
                                        src={service.image}
                                        alt={service.title}
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                                <h3 className="text-xl font-bold text-white mb-2">{service.title}</h3>
                                <p className="text-white/80 mb-4">{service.description}</p>
                                <div className="flex justify-between items-center">
                                    <span className="text-pink-400 font-semibold">{service.price}</span>
                                    <button
                                        onClick={() => setShowBookingModal(true)}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        Book Now
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </section>

                {/* Boat Services Section */}
                <section className="mb-16">
                    <div className="text-center mb-12">
                        <h2 className="text-4xl font-bold text-white mb-4">Boat Detailing Services</h2>
                        <p className="text-lg text-white/80 max-w-2xl mx-auto">
                            Protect your investment with our marine-specific detailing services designed for boats of all sizes.
                        </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {boatServices.map((service, index) => (
                            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all">
                                <div className="relative h-48 mb-4 rounded-lg overflow-hidden">
                                    <Image
                                        src={service.image}
                                        alt={service.title}
                                        fill
                                        className="object-cover"
                                    />
                                </div>
                                <h3 className="text-xl font-bold text-white mb-2">{service.title}</h3>
                                <p className="text-white/80 mb-4">{service.description}</p>
                                <div className="flex justify-between items-center">
                                    <span className="text-pink-400 font-semibold">{service.price}</span>
                                    <button
                                        onClick={() => setShowBookingModal(true)}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        Book Now
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                </section>

                {/* Why Choose Us Section */}
                <section className="mb-16">
                    <div className="text-center mb-12">
                        <h2 className="text-4xl font-bold text-white mb-4">Why Choose Our RV & Boat Services?</h2>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="text-4xl mb-4">🚐</div>
                            <h3 className="text-xl font-bold text-white mb-2">Mobile Convenience</h3>
                            <p className="text-white/80">We come to your location - whether at home, storage facility, or campground.</p>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl mb-4">🛠️</div>
                            <h3 className="text-xl font-bold text-white mb-2">Specialized Equipment</h3>
                            <p className="text-white/80">Professional-grade tools and products designed specifically for RVs and boats.</p>
                        </div>
                        <div className="text-center">
                            <div className="text-4xl mb-4">⭐</div>
                            <h3 className="text-xl font-bold text-white mb-2">Expert Technicians</h3>
                            <p className="text-white/80">Trained professionals who understand the unique needs of recreational vehicles.</p>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="text-center">
                    <div className="bg-gradient-to-r from-blue-600 to-pink-500 rounded-xl p-8">
                        <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
                        <p className="text-white/90 mb-6 max-w-2xl mx-auto">
                            Book your RV or boat detailing service today and experience the convenience of professional mobile detailing.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                                onClick={() => setShowBookingModal(true)}
                                className="px-8 py-4 bg-white text-blue-600 font-bold rounded-lg hover:bg-gray-100 transition-colors"
                            >
                                Book Now
                            </button>
                            <Link
                                href="/contact"
                                className="px-8 py-4 border-2 border-white text-white font-bold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
                            >
                                Get a Quote
                            </Link>
                        </div>
                    </div>
                </section>

                <div className="text-center mt-12">
                    <Link
                        href="/services"
                        className="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        ← View All Services
                    </Link>
                </div>
            </div>
        </div>
    );
}

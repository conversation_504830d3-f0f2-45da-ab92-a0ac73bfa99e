import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Newsletter | Detail On The Go',
    description: 'Subscribe to the Detail On The Go newsletter for updates, tips, and exclusive offers on mobile auto detailing services.',
    keywords: [
        'newsletter',
        'subscribe',
        'auto detailing newsletter',
        'Detail On The Go updates',
        'car care tips',
        'exclusive offers'
    ],
    alternates: {
        canonical: 'https://detailongo.com/newsletter/',
    },
    openGraph: {
        title: 'Newsletter | Detail On The Go',
        description: 'Subscribe for updates, tips, and exclusive offers from Detail On The Go.',
        url: 'https://detailongo.com/newsletter/',
        images: [
            {
                url: '/images/newsletter-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Newsletter - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Newsletter | Detail On The Go',
        description: 'Subscribe for updates, tips, and exclusive offers from Detail On The Go.',
        images: ['/images/newsletter-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "WebPage",
                        "name": "Newsletter",
                        "url": "https://detailongo.com/newsletter/",
                        "description": "Subscribe to the Detail On The Go newsletter for updates, tips, and exclusive offers on mobile auto detailing services.",
                        "publisher": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/newsletter-og.jpg",
                            "telephone": "******-615-6156"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}

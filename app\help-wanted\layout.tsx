import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Help Wanted | Careers at Detail On The Go',
    description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
    keywords: [
        'help wanted',
        'jobs',
        'careers',
        'auto detailing jobs',
        'mobile detailing jobs',
        'Detail On The Go jobs',
        'hiring',
        'employment'
    ],
    alternates: {
        canonical: 'https://www.detailongo.com/help-wanted/',
    },
    openGraph: {
        title: 'Help Wanted | Careers at Detail On The Go',
        description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
        url: 'https://detailongo.com/help-wanted/',
        images: [
            {
                url: '/images/help-wanted-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Help Wanted - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Help Wanted | Careers at Detail On The Go',
        description: 'Join the Detail On The Go team! View current job openings and career opportunities for mobile auto detailing professionals.',
        images: ['/images/help-wanted-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {children}
        </>
    );
}

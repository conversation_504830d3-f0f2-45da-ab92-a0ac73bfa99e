"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import ShinyText from "@/components/ShinyText";
import FranchiseApplication from "@/components/FranchiseApplication";
import BusinessModelQuizPopup from "@/components/BusinessModelQuizPopup";

const HelpWantedPage = () => {
    const [formData, setFormData] = useState({
        q1: "",
        q2: "",
        q3: "",
        q4: "",
        q5: "",
        q6: "",
        q7: "",
        q8: "",
        q9: "",
        q10: "",
        refName: "",
        refBusiness: "",
        refJobType: "",
        refYear: "",
        refLocation: "",
        refPhone: "",
        refEmail: "",
        firstName: "",
        lastName: "",
        phone: "",
        email: "",
        address: "",
        vehicle: "",
        socialMedia: "",
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitMessage, setSubmitMessage] = useState("");

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);
        setSubmitMessage("");

        const message = `
            <h2>Job Application</h2>
            <h3>Application Questions</h3>
            <ul>
                <li><strong>Independent Work:</strong> ${formData.q1}</li>
                <li><strong>Physical Demands:</strong> ${formData.q2}</li>
                <li><strong>Communication:</strong> ${formData.q3}</li>
                <li><strong>Prioritization:</strong> ${formData.q4}</li>
                <li><strong>Weather Conditions:</strong> ${formData.q5}</li>
                <li><strong>Quality Work:</strong> ${formData.q6}</li>
                <li><strong>Organization:</strong> ${formData.q7}</li>
                <li><strong>Learning Skills:</strong> ${formData.q8}</li>
                <li><strong>Reviews:</strong> ${formData.q9}</li>
                <li><strong>Read Description:</strong> ${formData.q10}</li>
            </ul>
            <h3>Reference</h3>
            <ul>
                <li><strong>Name:</strong> ${formData.refName}</li>
                <li><strong>Business Name:</strong> ${formData.refBusiness}</li>
                <li><strong>Job Type:</strong> ${formData.refJobType}</li>
                <li><strong>Year Worked:</strong> ${formData.refYear}</li>
                <li><strong>Location:</strong> ${formData.refLocation}</li>
                <li><strong>Phone:</strong> ${formData.refPhone}</li>
                <li><strong>Email:</strong> ${formData.refEmail}</li>
            </ul>
            <h3>Applicant Info</h3>
            <ul>
                <li><strong>First Name:</strong> ${formData.firstName}</li>
                <li><strong>Last Name:</strong> ${formData.lastName}</li>
                <li><strong>Phone:</strong> ${formData.phone}</li>
                <li><strong>Email:</strong> ${formData.email}</li>
                <li><strong>Address:</strong> ${formData.address}</li>
                <li><strong>Vehicle:</strong> ${formData.vehicle}</li>
            </ul>
            <h3>Social Media</h3>
            <p>${formData.socialMedia || "Not provided"}</p>
        `;

        try {
            const response = await fetch("/api/send-email", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    fromName: `${formData.firstName} ${formData.lastName}`,
                    fromEmail: formData.email,
                    to: "<EMAIL>",
                    subject: "Detail On The Go Job Application",
                    message,
                }),
            });

            if (!response.ok) {
                throw new Error("Failed to send email");
            }

            const result = await response.json();
            setSubmitMessage(result.message);
            setFormData({
                q1: "", q2: "", q3: "", q4: "", q5: "",
                q6: "", q7: "", q8: "", q9: "", q10: "",
                refName: "", refBusiness: "", refJobType: "", refYear: "",
                refLocation: "", refPhone: "", refEmail: "",
                firstName: "", lastName: "", phone: "", email: "",
                address: "", vehicle: "", socialMedia: ""
            });
        } catch (error) {
            if (error instanceof Error) {
                setSubmitMessage(`Error: ${error.message}`);
            } else {
                setSubmitMessage("An unknown error occurred.");
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <main className="min-h-screen bg-transparent font-[Helvetica,Arial,sans-serif]">
            <section className="w-full py-12 md:pt-16 md:pb-8">
                <div className="max-w-6xl mx-auto px-4 text-center">
                    <h1 className="mb-4">
                        <ShinyText
                            text="Run with the Cleanest Business on the Planet"
                            speed={5}
                            className="text-6xl md:text-6xl font-display font-bold italic text-white tracking-tight"
                        />
                    </h1>
                    <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                        Join the Detail On The Go team as a Mobile Detailing Specialist.
                    </p>

                    <div className="flex flex-col items-center space-y-6 px-8">
                        <div className="relative">
                            <span className="absolute -left-10 top-1/2 -translate-y-1/2 text-4xl z-20">
                                <span className="inline-block wave-animation">👉</span>
                            </span>
                            <a
                                href="#apply-here"
                                className="group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:translate-y-[-5px] hover:shadow-lg hover:shadow-pink-500 shiny-button border border-white"
                            >
                                <span className="relative z-10 italic font-bold text-xl">
                                    Ready to Join Us?
                                </span>
                                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </a>
                        </div>
                        <p className="text-white/90 text-lg font-semibold">
                            We're looking for dedicated individuals to join our team.
                        </p>


                    </div>

                </div>
                {/* Added Image */}
                <div className="w-full mt-8 max-w-4xl mx-auto">
                    <Image
                        src="/help-wanted-2.png"
                        alt="Join Our Team"
                        width={1200}
                        height={600}
                        className="rounded-lg shadow-xl border-4 border-white"
                    />
                </div>
            </section>

            {/* Job Overview Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Job Overview</h2>
                    <div className="space-y-8">
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Welcome!</h3>
                            <p className="text-gray-700 mb-4">
                                Here are a few reasons this role might not be a great fit:
                            </p>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li><strong>Independent Work:</strong> Post-training, you'll handle most detailing tasks independently and maintain your detail van—washing and organizing it daily.</li>
                                <li><strong>Physical Demands:</strong> The job requires stamina and precision over long hours.</li>
                                <li><strong>Effective Communication:</strong> Success hinges on clear interactions with both clients and team members.</li>
                                <li><strong>Review Collection:</strong> Gathering client reviews is essential, despite its challenges.</li>
                                <li><strong>Self-Motivation:</strong> You must be driven to complete jobs independently.</li>
                                <li><strong>Outdoor Conditions:</strong> The mobile nature of the role means you'll face varying weather conditions.</li>
                            </ul>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">That Being Said...</h3>
                            <p className="text-gray-700 mb-4">
                                We are seeking an individual who is eager to learn Detail On The Go and eventually manage a location of their own. We value a positive attitude and a flexible schedule, as this role involves handling a full day of labor-intensive detailing (8

                                + hours).
                            </p>
                            <p className="text-gray-700 mb-4">
                                Training is provided, along with company clothing, tools, chemicals, and transportation to the job(s).
                            </p>
                            <p className="text-gray-700 mb-4">
                                After completing training, you should be competent enough to perform tasks independently, with high quality, and in a timely manner. If you demonstrate the ability to communicate well with customers and perform both exterior washes and interior details to meet company standards, you will be hired on.
                            </p>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Requirements After Training Period</h3>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li>Demonstrated competence in conducting tasks independently.</li>
                                <li>Ability to perform tasks efficiently and in a timely manner.</li>
                                <li>Proper execution of exterior washes and interior detailing.</li>
                                <li>Capability to meet company quality standards on your own.</li>
                            </ul>
                            <p className="text-gray-700 mb-4">
                                After training, you will be working independently on most jobs, unless otherwise stated.
                            </p>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Dress & Company Policies</h3>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li><strong>Smoking/Vaping:</strong> Smoking or vaping is not permitted in or near any client vehicles.</li>
                                <li><strong>Dress Code:</strong> Employees must wear company logo-branded T-shirts (provided) and khaki brown pants (not provided). The logo-branded top must be worn on the outer layer, clearly visible to customers, and tucked in with a belt for a neat appearance. Regular washing is essential to maintain a clean, professional look.</li>
                                <li><strong>End-of-Day Procedures:</strong> At the end of each day, ensure all tools and equipment are properly stored; towels are returned for washing; trash is emptied; and any reports of low fuel, missing equipment, or chemicals are submitted for replenishment. Additionally, vacuums must be cleaned out at least once a week. This time is compensated, so all tasks must be completed before finishing the day. If you drop off the vehicle at the end of the day, your compensation continues until that task is complete.</li>
                                <li><strong>Punctuality:</strong> Arrive at the van meet point early enough to reach your first appointment at least five minutes before the scheduled start. You are paid starting from when you arrive at the work vehicle.</li>
                                <li><strong>Time Off Requests:</strong> Employees must submit time off requests with at least two weeks' notice.</li>
                            </ul>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Requirements</h3>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li>Reliable transportation to reach the work vehicle</li>
                                <li>A smartphone with internet access</li>
                                <li>Tan or brown khakis/pants, or tan/brown shorts</li>
                                <li>Good hygiene</li>
                                <li>Insurance ($50–$70 per month)</li>
                            </ul>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Frequently Requested Services</h3>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li>Interior Detailing</li>
                                <li>Exterior Washing</li>
                                <li>Fabric Shampoo & Extraction</li>
                                <li>Pet Hair Removal</li>
                                <li>Headlight Restoration</li>
                                <li>Waxing</li>
                                <li>Ceramic Coating</li>
                                <li>Paint Correction</li>
                            </ul>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Detail Durations</h3>
                            <p className="text-gray-700 mb-2"><strong>First-Time Details:</strong></p>
                            <p className="text-gray-700 mb-2">Single Detailer: 3 to 4.5 hours</p>
                            <p className="text-gray-700 mb-2"><strong>Recurring Details:</strong></p>
                            <p className="text-gray-700 mb-4">Single Detailer: 30 minutes to 1.5 hours</p>
                            <p className="text-gray-700">
                                Our target is at least two first-time details per day, with the first appointment starting promptly at 8:00 a.m. at the customer's location. This requires arriving at the work van early enough to travel safely and within the speed limit to reach the customer by 8:00 a.m.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Dress Code Section */}
            <section className="w-full py-16 bg-pink-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">Dress Code</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Hats</h4>
                            <p className="text-gray-200">White or blue hats are allowed after approval. (Not Provided)</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Jackets/Coats</h4>
                            <p className="text-gray-200">Company hoodies/jackets/shirts are supplied and must be worn on the outer layer during all detailing sessions. (Provided as needed)</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Shirts</h4>
                            <p className="text-gray-200">Company shirts are provided and must be worn and tucked in whenever detailing. (Provided)</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Belt</h4>
                            <p className="text-gray-200">DOG Detailers wear tan brown pants or shorts depending on the weather. (Not Provided)</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Pants/Shorts</h4>
                            <p className="text-gray-200">DOG Detailers wear tan brown pants or shorts depending on the weather. (Not Provided)</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-lg p-6 rounded-xl border border-white/20">
                            <h4 className="text-lg font-semibold mb-2 text-white">Shoes/Sandals</h4>
                            <p className="text-gray-200">Shoes, Sandals, and Boots are all allowed if after approval. We want our detailers to be comfortable and safe. Flip flops are not allowed. (Not provided)</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Compensation & Pay Section */}
            <section className="w-full py-16 bg-blue-600/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-white">Compensation & Pay</h2>
                    <div className="space-y-8">
                        <div>
                            <p className="text-gray-200 mb-4">
                                You'll be engaged as a contractor, which entails the responsibility of filing out at 1099 form and providing your own insurance and managing your own taxes. Detail On The Go facilitates the process by offering readily available insurance options for each contractor, enabling quick and easy setup at minimal costs.
                            </p>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-white">Detailers Wage</h3>
                            <p className="text-gray-200 mb-2">
                                Starting at $18/hr, after a 40-day period, the rate increases to $19/hr. Raises are based on merit thereafter. Future reviews enable us to assess performance and potentially adjust compensation accordingly. Detail On The Go is the fastest growing mobile detailing business in Kansas. We are looking for dedicated team members to grow with us.
                            </p>
                            <p className="text-gray-200 mb-2">
                                For each Google review left by a customer for a detailer on a job well done, the detailer will be rewarded $10. Detailers must keep track of any reviews left and notify the team to be compensated.
                            </p>
                        </div>
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-white">Salesperson Wage</h3>
                            <p className="text-gray-200 mb-2">
                                Starting at base wage of $8 per hour, supplemented by a commission of 10% for each detail they successfully book and which is subsequently completed and paid for. Additionally, there will be a review after the first 5 sales to evaluate performance and potentially adjust compensation accordingly.
                            </p>
                        </div>
                        <div>
                            <p className="text-gray-200">
                                Your pay check will be deposited every Tuesday.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Growth Section */}
            <section className="w-full py-16 bg-gradient-to-br from-white/50 to-blue-100/50 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Growth 📈💸</h2>
                    <div className="space-y-8">
                        <div>
                            <p className="text-gray-700 mb-4">
                                Do you want to work for yourself eventually? Detail On The Go may be perfect for you.
                            </p>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li>Show up on time.</li>
                                <li>Demonstrate competence in your work.</li>
                                <li>Follow the methods of Detail On The Go.</li>
                                <li>Communicate effectively with teammates and customers.</li>
                                <li>Be open to receiving and acting on critical feedback.</li>
                                <li>Consistently put in effort and dedication to your tasks.</li>
                            </ul>
                            <p className="text-gray-700">
                                If you're able to do these things, you will be paid accordingly and given opportunities for growth, including the opportunity to eventually own your own Detail On The Go.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* How Many Hours Per Week Section */}
            <section className="w-full py-16 bg-white/90 backdrop-blur-xl">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">How Many Hours Per Week?</h2>
                    <div className="space-y-8">
                        <div>
                            <h3 className="text-2xl font-semibold mb-4 text-gray-800">Workload Summary</h3>
                            <ul className="list-disc list-inside text-gray-700 mb-4">
                                <li><strong>Daily Work Hours:</strong> For most of the year, you'll work about 6 to 9 hours a day, especially during the busy season from March to November. During December and January, you'll work fewer hours, with all work scheduled on the same day(s) of the week.</li>
                                <li><strong>Weekly Work Hours:</strong> Expect around 30-50 hours a week during the busy months, with lighter weeks in the slower months (December and January).</li>
                            </ul>
                            <p className="text-gray-700">
                                In short, your workdays and hours will vary, with full, steady schedules most of the year and lighter, more flexible schedules during the winter.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Apply Here Section */}
            <section className="w-full py-16 bg-gray-200/90 backdrop-blur-xl" id="apply-here">
                <div className="max-w-6xl mx-auto px-4">
                    <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">Apply Here</h2>
                    <div className="bg-white/80 backdrop-blur-lg p-8 rounded-xl border border-white/30 shadow-md">
                        <p className="text-gray-700 mb-4 font-bold text-lg">
                            Join the Hustlers – We're Seeking 🅰️ Players
                        </p>
                        <p className="text-gray-700 mb-4">
                            At Detail On The Go, we don't just hire – we handpick driven individuals who share our passion for excellence and precision. Our team is the backbone of the fastest-growing mobile detailing business in Kansas, and we're looking for the best to join us. This isn't just a job; it's a chance to grow with a dynamic brand, master in-demand skills, and pave your way to leadership. Opportunities like this are rare, and we're selective. If you're ready to prove you belong, submit your application now to seize this exclusive opening.
                        </p>
                        {submitMessage && (
                            <p className={`text-center mb-4 ${submitMessage.includes("Error") ? "text-red-600" : "text-green-600"}`}>
                                {submitMessage}
                            </p>
                        )}
                        <form onSubmit={handleSubmit} className="space-y-8">
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Application Questions</h3>
                                <div className="space-y-6">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Can you describe a time when you worked independently for an extended period? How did you stay focused and motivated?</label>
                                        <textarea
                                            name="q1"
                                            value={formData.q1}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"
                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">This job requires stamina and attention to detail over long hours. How have you managed physically demanding work in the past?</label>
                                        <textarea
                                            name="q2"
                                            value={formData.q2}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">How do you approach communicating with clients or team members when handling feedback or solving problems on the job?</label>
                                        <textarea
                                            name="q3"
                                            value={formData.q3}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">You'll often be responsible for completing multiple jobs in a day. How do you prioritize tasks to ensure deadlines are met?</label>
                                        <textarea
                                            name="q4"
                                            value={formData.q4}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">You'll be working outside in varying weather conditions. How do you prepare yourself for challenges like heat, cold, or rain?</label>
                                        <textarea
                                            name="q5"
                                            value={formData.q5}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">When working solo, how do you ensure you maintain high-quality work and stay efficient throughout the day?</label>
                                        <textarea
                                            name="q6"
                                            value={formData.q6}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Maintaining a clean and organized work van is a daily responsibility. Can you share how you've stayed organized in a past role?</label>
                                        <textarea
                                            name="q7"
                                            value={formData.q7}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Training is provided to help you succeed. How do you typically approach learning new skills, and how do you ensure you meet performance standards?</label>
                                        <textarea
                                            name="q8"
                                            value={formData.q8}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">How comfortable are you with asking someone to leave a review for you?</label>
                                        <textarea
                                            name="q9"
                                            value={formData.q9}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Did you read the entire job description and summary on this page?</label>
                                        <textarea
                                            name="q10"
                                            value={formData.q10}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            rows={4}
                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Reference</h3>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">Name</label>
                                        <input
                                            type="text"
                                            name="refName"
                                            value={formData.refName}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Business Name</label>
                                        <input
                                            type="text"
                                            name="refBusiness"
                                            value={formData.refBusiness}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Job Type</label>
                                        <input
                                            type="text"
                                            name="refJobType"
                                            value={formData.refJobType}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Year Worked</label>
                                        <input
                                            type="text"
                                            name="refYear"
                                            value={formData.refYear}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Location of Business</label>
                                        <input
                                            type="text"
                                            name="refLocation"
                                            value={formData.refLocation}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Phone Number (to reach reference)</label>
                                        <input
                                            type="text"
                                            name="refPhone"
                                            value={formData.refPhone}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Email (to reach reference)</label>
                                        <input
                                            type="email"
                                            name="refEmail"
                                            value={formData.refEmail}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Your Info</h3>
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-gray-700 mb-2">First Name</label>
                                        <input
                                            type="text"
                                            name="firstName"
                                            value={formData.firstName}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Last Name</label>
                                        <input
                                            type="text"
                                            name="lastName"
                                            value={formData.lastName}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Phone</label>
                                        <input
                                            type="text"
                                            name="phone"
                                            value={formData.phone}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Email</label>
                                        <input
                                            type="email"
                                            name="email"
                                            value={formData.email}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Address</label>
                                        <input
                                            type="text"
                                            name="address"
                                            value={formData.address}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-gray-700 mb-2">Year, Make & Model of your vehicle</label>
                                        <input
                                            type="text"
                                            name="vehicle"
                                            value={formData.vehicle}
                                            onChange={handleChange}
                                            className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                            placeholder="Type your answer here..."
                                            required
                                        />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-2xl font-semibold mb-4 text-gray-800">Social Media</h3>
                                <p className="text-gray-700 mb-2">Please paste at least one social media link</p>
                                <input
                                    type="text"
                                    name="socialMedia"
                                    value={formData.socialMedia}
                                    onChange={handleChange}
                                    className="w-full p-2 border border-gray-300 rounded-lg text-black"

                                    placeholder="Type your answer here..."
                                />
                                <p className="text-sm text-gray-500 mt-2">
                                    As part of our hiring process, we like to get a sense of a candidate's professional presence and vibe. If you're comfortable sharing, we'd like to view your social media profiles (e.g., LinkedIn, Instagram, or Facebook) to better understand how you present yourself and engage with others. This step is optional and will only be used to assess alignment with our company culture.
                                </p>
                            </div>
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className={`group relative bg-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-pink-600 transition-all transform hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:shadow-pink-500 border border-white ${isSubmitting ? "opacity-50 cursor-not-allowed" : ""}`}
                            >
                                <span className="relative z-10 italic font-bold text-xl">Submit</span>
                                <span className="absolute inset-0 bg-gradient-to-r from-pink-500 to-pink-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></span>
                            </button>
                        </form>
                    </div>
                </div>
            </section>

            {/* Schema Markup for Job Posting */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "JobPosting",
                        "title": "Mobile Detailing Specialist",
                        "description": "Join the Detail On The Go team as a Mobile Detailing Specialist. We're looking for dedicated individuals who are eager to learn and eventually manage a location of their own. Training is provided, along with company clothing, tools, chemicals, and transportation.",
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "Detail On The Go",
                            "value": "mobile-detailing-specialist-2025"
                        },
                        "datePosted": "2025-01-01",
                        "validThrough": "2025-12-31",
                        "employmentType": "CONTRACTOR",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "sameAs": "https://www.detailongo.com",
                            "logo": "https://www.detailongo.com/logo.png"
                        },
                        "jobLocation": [
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Lawrence",
                                    "addressRegion": "KS",
                                    "addressCountry": "US"
                                }
                            },
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Kansas City",
                                    "addressRegion": "MO",
                                    "addressCountry": "US"
                                }
                            },
                            {
                                "@type": "Place",
                                "address": {
                                    "@type": "PostalAddress",
                                    "addressLocality": "Denver",
                                    "addressRegion": "CO",
                                    "addressCountry": "US"
                                }
                            }
                        ],
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "USD",
                            "value": {
                                "@type": "QuantitativeValue",
                                "value": 18,
                                "unitText": "HOUR"
                            }
                        },
                        "workHours": "30-50 hours per week",
                        "qualifications": "Reliable transportation, smartphone with internet access, good hygiene, ability to work independently",
                        "responsibilities": "Mobile vehicle detailing, customer service, equipment maintenance, quality control",
                        "benefits": "Training provided, company equipment, growth opportunities, performance bonuses"
                    }),
                }}
            />
        </main>
    );
};

export default HelpWantedPage;